#!/bin/bash

#===============================================================================
# PostgreSQL 和 TDengine 数据库执行器
#===============================================================================
# 功能描述: 提供数据库连接、SQL执行、版本管理等核心功能
# 支持数据库: PostgreSQL, TDengine
# 创建时间: 2024年
#
# 主要功能:
# - PostgreSQL 数据库连接和操作
# - TDengine 时序数据库连接和操作
# - 数据库版本管理和迁移
# - SQL 文件执行和错误处理
#===============================================================================

#===============================================================================
# PostgreSQL 数据库操作函数
#===============================================================================

#-------------------------------------------------------------------------------
# 函数名: psqlCreateConn
# 功能: 根据传入的参数动态创建PostgreSQL数据库连接字符串
# 参数:
#   $1: 容器名称 (e.g., "base_postgres")
#   $2: 用户名
#   $3: 密码
#   $4: 数据库名称
#   $5: Schema名称 (可选, 默认为 "public")
#   $6: 主机地址 (可选, 默认为 "localhost")
#   $7: 端口 (可选, 默认为 "5432")
#   $8: 超级用户名 (可选, 默认为与用户名相同)
# 返回: 格式化的 psql 连接字符串
# 说明: 此函数将密码进行URL编码，并构建一个完整的 `docker exec` 命令
#       用于后续的数据库操作。
#-------------------------------------------------------------------------------
psqlCreateConn() {
  local container="$1"
  local username="$2"
  local password="$3"
  local database="$4"
  local schema="${5:-public}"
  local host="${6:-localhost}"
  local port="${7:-5432}"
  local admin_user="${8:-$username}"

  # 检查必要参数
  if [ -z "$container" ] || [ -z "$username" ] || [ -z "$password" ] || [ -z "$database" ]; then
    echo "[ERROR]: psqlCreateConn - 容器、用户名、密码、数据库和端口是必需的" >&2
    return 1
  fi

  # 对密码进行URL编码
  local password_encoded
  password_encoded=$(echo -n "${password}" | sed \
    -e 's/%/%25/g' -e 's/ /%20/g' -e 's/!/%21/g' -e 's/"/%22/g' \
    -e '#/%23/g' -e 's/\$/%24/g' -e 's/&/%26/g' -e "s/'/%27/g" \
    -e 's/(/%28/g' -e 's/)/%29/g' -e 's/\*/%2a/g' -e 's/+/%2b/g' \
    -e 's/,/%2c/g' -e 's/\//%2f/g' -e 's/:/%3a/g' -e 's/;/%3b/g' \
    -e 's/=/%3d/g' -e 's/?/%3f/g' -e 's/@/%40/g' -e 's/\[/%5b/g' \
    -e 's/\]/%5d/g' -e 's/\^/%5e/g'
  )

  # 构建连接字符串
  local conn_string="docker exec -i ${container} psql postgres://${admin_user}:${password_encoded}@${host}:${port}/${database}?options=--search_path%3d${schema}"

  echo "$conn_string"
}

#-------------------------------------------------------------------------------
# 函数名: psqlCheckConn
# 功能: 检查PostgreSQL数据库连接是否正常
# 参数:
#   $1: PostgreSQL连接字符串
# 返回: 0(连接成功), 非0(连接失败)
# 说明: 通过执行简单的SELECT语句来测试数据库连接
#       使用静默模式避免输出干扰
#-------------------------------------------------------------------------------
psqlCheckConn() {
  local psql="$1"

  # 检查参数是否为空
  if [ -z "$psql" ]; then
    echo "[ERROR]: PostgreSQL连接字符串不能为空" >&2
    return 1
  fi

  # 执行简单查询测试连接
  set +e
  #echo "[DEBUG]: $psql -c \"SELECT 1;\"" >&2
  $psql -c "SELECT 1;" 1>/dev/null 2>&1
  local result=$?

  return $result
}

#-------------------------------------------------------------------------------
# 函数名: psqlGetSchema
# 功能: 从PostgreSQL连接字符串中提取schema名称
# 参数:
#   $1: PostgreSQL连接字符串
# 返回: schema名称
# 说明: 通过正则表达式解析连接字符串中的search_path参数
#       URL编码中%3d表示等号(=)
#-------------------------------------------------------------------------------
psqlGetSchema() {
  local $sql_cmd="$1"

  # 检查参数是否为空
  if [ -z "$sql_cmd" ]; then
    echo "[ERROR]: PostgreSQL连接字符串不能为空" >&2
    return 1
  fi

  # 使用正则表达式提取schema名称
  if [[ "$sql_cmd" =~ search_path%3d(.*)$ ]]; then
    echo "${BASH_REMATCH[1]}"
  else
    echo "[ERROR]: 无法从连接字符串中提取schema信息" >&2
    return 2
  fi
}

#-------------------------------------------------------------------------------
# 函数名: psqlCheckSchema
# 功能: 检查PostgreSQL数据库中指定的schema是否存在
# 参数:
#   $1: PostgreSQL连接字符串
#   $2: schema名称（可选，如果不提供则从连接字符串中提取）
# 返回: 0(schema存在), 1(schema不存在), 2(检查失败)
# 说明: 从连接字符串中提取schema名称，然后查询information_schema验证其存在性
#       使用系统表information_schema.schemata进行验证
#       返回状态码而不是直接退出程序
#-------------------------------------------------------------------------------
psqlCheckSchema() {
  local psql="$1"
  local schema_name="$2"

  # 检查参数是否为空
  if [ -z "$psql" ]; then
    echo "[ERROR]: PostgreSQL 连接字符串不能为空" >&2
    return 2
  fi

  # 如果没有提供 schema 名称，从连接字符串中提取
  if [ -z "$schema_name" ]; then
    schema_name=$(psqlGetSchema "$psql")
    if [ $? -ne 0 ] || [ -z "$schema_name" ]; then
      echo "[ERROR]: 无法从连接字符串中提取 schema 信息" >&2
      return 2
    fi
  fi

  # 临时禁用 set -e 来捕获查询错误
  set +e

  echo "[INFO]: 检查数据库 schema: $schema_name" > /dev/tty

  # 查询schema是否存在 - 优先使用 information_schema（SQL 标准）
  local sqlRes
  #echo "[DEBUG]: $psql -c \"SELECT schema_name FROM information_schema.schemata WHERE schema_name = '$schema_name';\"" >&2
  sqlRes=$($psql -c "SELECT schema_name FROM information_schema.schemata WHERE schema_name = '$schema_name';" 2>&1)
  local query_result=$?

  # 检查查询结果
  #echo "[DEBUG]: 查询返回码: $query_result" >&2
  #echo "[DEBUG]: 查询结果: $sqlRes" >&2

  # 如果查询执行失败，尝试备用方案
  if [[ $query_result -ne 0 || "$sqlRes" == *"(0 rows)"* ]]; then
    echo "[INFO]: information_schema 查询失败，尝试使用 pg_namespace..." >&2

    # 备用方案：使用 pg_namespace
    sqlRes=$($psql -c "SELECT nspname FROM pg_namespace WHERE nspname = '$schema_name';" 2>&1)
    query_result=$?

    if [ $query_result -ne 0 ]; then
      echo "[ERROR]: 两种查询方式都失败: $sqlRes" >&2
      # 重新启用 set -e
      set -e
      return 2
    fi
    #echo "[DEBUG]: pg_namespace 查询结果: $sqlRes" > /dev/tty
  fi

  # 检查是否通过任意方式找到 schema
  if [[ "$sqlRes" == *"(0 rows)"* ]]; then
    #echo "[ERROR]: 数据库中不存在 schema='$schema'，请检查数据库配置" >&2
    #echo "[DEBUG]: 可用的 schema 列表:" > /dev/tty
    $psql -c "SELECT schema_name FROM information_schema.schemata;" 2>&1 >&2 || $psql -c "SELECT nspname FROM pg_namespace;" 2>&1 >&2
    return 1
  else
    echo "[INFO]: Schema '$schema' 验证通过" > /dev/tty
  fi

  # 重新启用 set -e
  set -e
}

#===============================================================================
# 数据库管理函数
#===============================================================================

#-------------------------------------------------------------------------------
# 函数名: psqlCreateDatabase
# 功能: 检查数据库是否存在，不存在则创建；检查schema是否存在，不存在则创建
# 参数:
#   $1: PostgreSQL连接字符串（连接到postgres数据库）
#   $2: 要创建的数据库名称
#   $3: 要创建的schema名称（可选，默认为public）
# 返回: 0(成功), 非0(失败)
# 说明: 先检查数据库是否存在，如果不存在则创建新数据库
#       然后检查指定schema是否存在，如果不存在则创建
#       使用postgres数据库作为管理连接来创建其他数据库
#-------------------------------------------------------------------------------
psqlCreateDatabase() {
  local psql="$1"
  local database_name="$2"
  local schema_name="$3"

  # 检查参数是否为空
  if [ -z "$psql" ]; then
    echo "[ERROR]: PostgreSQL连接字符串不能为空" >&2
    return 1
  fi

  if [ -z "$database_name" ]; then
    echo "[ERROR]: 数据库名称不能为空" >&2
    return 1
  fi

  echo "[INFO]: 检查数据库是否存在: $database_name"

  # 临时禁用 set -e
  set +e

  # 检查数据库是否存在
  local check_result
  check_result=$($psql -c "SELECT 1 FROM pg_database WHERE datname = '$database_name';" 2>&1)
  local query_result=$?

  # 重新启用 set -e
  set -e

  if [ $query_result -ne 0 ]; then
    echo "[ERROR]: 检查数据库存在性时发生错误: $check_result" >&2
    return 1
  fi

  # 如果数据库不存在，则创建
  if [[ "$check_result" == *"(0 rows)"* ]]; then
    echo "[INFO]: 数据库 '$database_name' 不存在，正在创建..."

    # 临时禁用 set -e
    set +e

    local create_result
    create_result=$($psql -c "CREATE DATABASE \"$database_name\";" 2>&1)
    local create_status=$?

    # 重新启用 set -e
    set -e

    if [ $create_status -eq 0 ]; then
      echo "[INFO]: 数据库 '$database_name' 创建成功"
    else
      echo "[ERROR]: 数据库 '$database_name' 创建失败: $create_result" >&2
      return 1
    fi
  else
    echo "[INFO]: 数据库 '$database_name' 已存在"
  fi

  # 如果指定了schema且不是public，需要检查并创建schema
  if [ -n "$schema_name" ] && [ "$schema_name" != "public" ]; then
    echo "[INFO]: 检查并创建schema: $schema_name"

    # 构建连接到目标数据库的连接字符串
    local target_psql
    target_psql=$(psqlModifyConnectionDatabase "$psql" "$database_name" "public")
    if [ $? -ne 0 ] || [ -z "$target_psql" ]; then
      echo "[ERROR]: 构建目标数据库连接字符串失败" >&2
      return 1
    fi

    # 使用psqlCheckSchema检查schema是否存在
    local schema_check_result
    psqlCheckSchema "$target_psql" "$schema_name"
    schema_check_result=$?

    if [ $schema_check_result -eq 2 ]; then
      echo "[ERROR]: 检查schema时发生错误" >&2
      return 1
    elif [ $schema_check_result -eq 1 ]; then
      # schema不存在，需要创建
      echo "[INFO]: Schema '$schema_name' 不存在，正在创建..."

      # 临时禁用 set -e
      set +e

      local schema_create_result
      schema_create_result=$($target_psql -c "CREATE SCHEMA \"$schema_name\";" 2>&1)
      local schema_create_status=$?

      # 重新启用 set -e
      set -e

      if [ $schema_create_status -eq 0 ]; then
        echo "[INFO]: Schema '$schema_name' 创建成功"
      else
        echo "[ERROR]: Schema '$schema_name' 创建失败: $schema_create_result" >&2
        return 1
      fi
    fi
    # schema_check_result -eq 0 表示schema已存在，无需处理
  fi

  return 0
}

#-------------------------------------------------------------------------------
# 函数名: psqlModifyConnectionDatabase
# 功能: 修改PostgreSQL连接字符串中的数据库名称和schema
# 参数:
#   $1: 原始PostgreSQL连接字符串
#   $2: 新的数据库名称
#   $3: 新的schema名称（可选，默认为public）
# 返回: 修改后的连接字符串
# 说明: 将连接字符串中的数据库部分替换为指定的数据库名称
#       同时修改search_path参数为指定的schema（默认为public）
#-------------------------------------------------------------------------------
psqlModifyConnectionDatabase() {
  local original_psql="$1"
  local new_database="$2"
  local new_schema="${3:-public}"

  # 检查参数是否为空
  if [ -z "$original_psql" ]; then
    echo "[ERROR]: 原始PostgreSQL连接字符串不能为空" >&2
    return 1
  fi

  if [ -z "$new_database" ]; then
    echo "[ERROR]: 新数据库名称不能为空" >&2
    return 1
  fi

  # 使用sed替换连接字符串中的数据库名称和schema
  # 1. 先替换数据库名称: /数据库名?options 替换为 /新数据库名?options
  # 2. 再替换search_path: search_path%3d旧schema 替换为 search_path%3d新schema
  local modified_psql
  modified_psql=$(echo "$original_psql" | sed "s|/[^/?]*?|/$new_database?|" | sed "s|search_path%3d[^&]*|search_path%3d$new_schema|")

  echo "$modified_psql"
}

#-------------------------------------------------------------------------------
# 函数名: psqlExecSqlFileWithDatabase
# 功能: 执行SQL文件，支持数据库检查和创建
# 参数:
#   $1: PostgreSQL连接字符串（连接到postgres数据库）
#   $2: SQL文件路径
#   $3: 目标数据库名称（可选，如果不提供则使用连接字符串中的数据库）
# 返回: 无（执行失败时直接退出程序）
# 说明: 1. 检查SQL文件是否存在，不存在则跳过
#       2. 检查目标数据库是否存在，不存在则创建
#       3. 修改连接字符串指向目标数据库
#       4. 执行SQL文件
#-------------------------------------------------------------------------------
psqlExecSqlFileWithDatabase() {
  local psql="$1"
  local sqlFile="$2"
  local target_database="$3"
  local target_schema="${4:-public}"

  # 检查参数是否为空
  if [ -z "$psql" ]; then
    echo "[ERROR]: PostgreSQL连接字符串不能为空" >&2
    exit 1
  fi
  # 从连接字符串中提取数据库名称
  if [[ ! "$psql" =~ /([^/?]+)\? ]]; then
    echo "[ERROR]: 无法从 $psql 提取数据库名称" >&2
    exit 1
  fi

  if [ -z "$sqlFile" ]; then
    echo "[ERROR]: SQL文件路径不能为空" >&2
    exit 1
  fi

  # 1. 检查SQL文件是否存在
  if [ ! -e "$sqlFile" ]; then
    echo "[INFO]: SQL文件不存在，跳过执行: $sqlFile"
    return 0
  fi

  # 检查SQL文件是否可读
  if [ ! -r "$sqlFile" ]; then
    echo "[ERROR]: SQL文件不可读: $sqlFile" >&2
    exit 1
  fi

  echo "[INFO]: 准备执行SQL文件: $sqlFile"

  # 2. 检查数据库是否存在，不存在则创建
  if ! psqlCreateDatabase "$psql" "$target_database" "$target_schema"; then
    echo "[ERROR]: 数据库检查或创建失败: $database_name" >&2
    exit 1
  fi

  # 3. 修改连接字符串指向目标数据库，并设置schema为public
  local target_psql
  target_psql=$(psqlModifyConnectionDatabase "$psql" "$target_database" "$target_schema")
  if [ $? -ne 0 ] || [ -z "$target_psql" ]; then
    echo "[ERROR]: 修改连接字符串失败" >&2
    exit 1
  fi

  echo "[INFO]: 使用数据库连接: $target_database $target_schema"

  # 4. 执行SQL文件
  psqlExecSqlFile "$target_psql" "$sqlFile"
}

#===============================================================================
# SQL 执行函数
#===============================================================================

#-------------------------------------------------------------------------------
# 函数名: psqlExecSqlFile
# 功能: 执行指定的SQL文件
# 参数:
#   $1: PostgreSQL连接字符串
#   $2: SQL文件路径
# 返回: 无（执行失败时直接退出程序）
# 说明: 读取SQL文件并在数据库中执行，捕获错误信息
#       如果执行过程中出现ERROR则输出错误信息并退出程序
#       只捕获stderr以检测错误，stdout被重定向到/dev/null
#-------------------------------------------------------------------------------
psqlExecSqlFile() {
  local psql="$1"
  local sqlFile="$2"

  # 检查参数是否为空
  if [ -z "$psql" ]; then
    echo "[ERROR]: PostgreSQL连接字符串不能为空" >&2
    exit 1
  fi

  if [ -z "$sqlFile" ]; then
    echo "[ERROR]: SQL文件路径不能为空" >&2
    exit 1
  fi

  # 检查SQL文件是否存在
  if [ ! -f "$sqlFile" ]; then
    echo "[ERROR]: SQL文件不存在: $sqlFile" >&2
    exit 1
  fi

  # 检查SQL文件是否可读
  if [ ! -r "$sqlFile" ]; then
    echo "[ERROR]: SQL文件不可读: $sqlFile" >&2
    exit 1
  fi

  echo "[INFO]: 正在执行SQL文件: $sqlFile" > /dev/tty

  # 临时禁用 set -e 来处理 SQL 执行过程中的非致命错误
  set +e

  # 执行SQL文件，只捕获错误输出
  local sqlErr
  echo "[DEBUG]: $psql < $sqlFile" > /dev/tty
  sqlErr=$($psql < "$sqlFile" 2>&1 1>/dev/null)
  local exec_result=$?

  # 重新启用 set -e
  set -e

  # 检查执行结果
  if [ $exec_result -ne 0 ] || [[ "$sqlErr" == *"[ERROR]"* ]]; then
    echo "[ERROR]: SQL文件执行失败: $sqlFile" >&2
    echo "[ERROR]: 错误信息: $sqlErr" >&2
    exit 1
  elif [ -n "$sqlErr" ]; then
    # 输出警告信息（如果有的话）
    echo "WARNING: SQL执行警告: $sqlErr" >&2
  fi

  echo "[INFO]: SQL文件执行成功: $sqlFile" > /dev/tty
}

#-------------------------------------------------------------------------------
# 函数名: psqlExecSql
# 功能: 执行指定的SQL语句
# 参数:
#   $1: PostgreSQL连接字符串
#   $2: SQL语句字符串
# 返回: SQL执行结果（包括标准输出和错误输出）
# 说明: 直接执行SQL语句并返回完整的执行结果
#       不进行错误处理，由调用方决定如何处理结果
#       适用于需要获取查询结果的场景
#-------------------------------------------------------------------------------
psqlExecSql() {
  local psql="$1"
  local sql="$2"

  # 检查参数是否为空
  if [ -z "$psql" ]; then
    echo "[ERROR]: PostgreSQL连接字符串不能为空" >&2
    return 1
  fi

  if [ -z "$sql" ]; then
    echo "[ERROR]: SQL语句不能为空" >&2
    return 1
  fi

  # 执行SQL语句并返回结果
  echo "[DEBUG]: $psql -c \"$sql\"" >&2
  $psql -c "$sql" 2>&1
}

#===============================================================================
# TDengine 时序数据库操作函数
#===============================================================================

#-------------------------------------------------------------------------------
# 函数名: tdsqlCheckConn
# 功能: 检查TDengine时序数据库连接是否正常
# 参数:
#   $1: TDengine连接字符串
# 返回: 0(连接成功), 非0(连接失败)
# 说明: 通过执行show users命令来测试TDengine数据库连接
#       -s参数表示静默模式执行
#-------------------------------------------------------------------------------
tdsqlCheckConn() {
  local tdsql="$1"

  # 检查参数是否为空
  if [ -z "$tdsql" ]; then
    echo "[ERROR]: TDengine连接字符串不能为空" >&2
    return 1
  fi

  # 执行show users命令测试连接
  $tdsql -s "SHOW USERS;" 1>/dev/null 2>&1
  return $?
}

#-------------------------------------------------------------------------------
# 函数名: tdsqlImport
# 功能: 导入TDengine初始化SQL脚本
# 参数:
#   $1: TDengine连接字符串
# 返回: 0(导入成功), 非0(导入失败)
# 说明: 执行TDengine容器内的初始化SQL脚本文件
#       脚本路径为容器内的固定路径
#-------------------------------------------------------------------------------
tdsqlImport() {
  local tdsql="$1"
  local init_script="/home/<USER>/init-taosdb.sql"

  # 检查参数是否为空
  if [ -z "$tdsql" ]; then
    echo "[ERROR]: TDengine连接字符串不能为空" >&2
    return 1
  fi

  echo "[INFO]: 正在导入TDengine初始化脚本: $init_script" > /dev/tty

  # 执行初始化脚本
  $tdsql -s "SOURCE $init_script;" 1>/dev/null 2>&1
  local result=$?

  if [ $result -eq 0 ]; then
    echo "[INFO]: TDengine初始化脚本导入成功" > /dev/tty
  else
    echo "[ERROR]: TDengine初始化脚本导入失败" >&2
  fi

  return $result
}

#-------------------------------------------------------------------------------
# 函数名: tdsqlCheckInit
# 功能: 检查TDengine时序数据库初始化状态
# 参数: 无
# 返回: 初始化版本信息或退出程序
# 说明: 通过查询log.log表来验证TDengine数据库是否正确初始化
#       如果初始化成功则输出调试信息，失败则退出程序
#       使用固定的数据库连接参数
#-------------------------------------------------------------------------------
tdsqlCheckInit() {
  local container_name="tdengine"
  local db_host="tdengine"
  local db_user="root"
  local db_pass="bdtdtd@123"
  local test_query="SELECT TODAY() FROM log.log;"

  echo "[INFO]: 检查TDengine数据库初始化状态..." > /dev/tty

  # 执行测试查询
  local curVer
  curVer=$(docker exec -i "$container_name" taos -h "$db_host" -u"$db_user" -p"$db_pass" -s "$test_query" 2>&1)
  local query_result=$?

  # 分析查询结果
  if [[ "$curVer" == *"(Query OK)"* ]]; then
    echo "[DEBUG]: 时序数据库初始化验证成功" > /dev/tty
    return 0
  elif [[ "$curVer" == *"[ERROR]"* ]] || [ $query_result -ne 0 ]; then
    echo "[ERROR]: TDengine初始化检查失败，错误信息: $curVer" >&2
    return 1
  else
    # 提取版本信息（如果有的话）
    curVer=$(awk -F: 'NR == 3' <<< "$curVer" | sed 's/ //g')
    if [ -n "$curVer" ]; then
      echo "$curVer"
    fi
  fi
}

#===============================================================================
# 服务版本管理函数
#===============================================================================

#-------------------------------------------------------------------------------
# 函数名: psqlGetCurVer
# 功能: 获取PostgreSQL数据库中当前部署的版本号
# 参数:
#   $1: PostgreSQL连接字符串
# 返回: 当前版本号字符串（如果存在）
# 说明: 从databasechangelog表中查询最新的版本标签
#       如果表不存在或无记录则返回空，出错时退出程序
#       版本信息按执行时间倒序排列，取最新的一条记录
#-------------------------------------------------------------------------------
psqlGetCurVer() {
  local psql="$1"

  # 检查参数是否为空
  if [ -z "$psql" ]; then
    echo "[ERROR]: PostgreSQL连接字符串不能为空" >&2
    return 1
  fi

  # 提取schema名称
  local schema
  schema=$(psqlGetSchema "$psql")
  if [ $? -ne 0 ] || [ -z "$schema" ]; then
    echo "[ERROR]: 无法从连接字符串中提取schema信息" >&2
    return 1
  fi

  # 临时禁用 set -e
  set +e

  # 检查 databasechangelog 表是否存在
  local checkExist
  #echo "[DEBUG]: $psql -c \"SELECT 1 FROM information_schema.tables WHERE table_schema='$schema' AND table_name='databasechangelog';\"" >&2
  checkExist=$($psql -c "SELECT 1 FROM information_schema.tables WHERE table_schema='$schema' AND table_name='databasechangelog';" 2>&1)
  local check_result=$?

  if [ $check_result -ne 0 ]; then
    echo "[ERROR]: 检查 databasechangelog 表时发生错误: $checkExist" >&2
    exit 1
  elif [[ "$checkExist" == *"(0 rows)"* ]]; then
    echo "[DEBUG]: databasechangelog 表不存在，可能是全新安装" > /dev/tty
    return 0
  fi

  # 查询当前版本号
  local curVer
  echo "[DEBUG]: $psql -c \"SELECT tag FROM ${schema}.databasechangelog ORDER BY dateexecuted DESC LIMIT 1;\"" >&2
  curVer=$($psql -c "SELECT tag FROM ${schema}.databasechangelog ORDER BY dateexecuted DESC LIMIT 1;" 2>&1)
  local query_result=$?

  # 分析查询结果
  if [ $query_result -ne 0 ]; then
    echo "[ERROR]: 查询版本信息时发生错误: $curVer" >&2
    exit 1
  elif [[ "$curVer" == *"(0 rows)"* ]]; then
    echo "[DEBUG]: databasechangelog 表中没有版本记录" > /dev/tty
    return 0
  elif [[ "$curVer" == *"[ERROR]"* ]] || [[ "$curVer" == *"[ERROR]"* ]]; then
    echo "[ERROR]: 查询版本信息失败: $curVer" >&2
    exit 1
  else
    # 提取版本号（通常在第3行）
    curVer=$(awk -F: 'NR == 3' <<< "$curVer" | sed 's/^[[:space:]]*//;s/[[:space:]]*$//')
    if [ -n "$curVer" ]; then
      echo "$curVer"
    fi
  fi
}

#-------------------------------------------------------------------------------
# 函数名: psqlCreateCvsTable
# 功能: 创建数据库版本管理相关的表结构
# 参数:
#   $1: PostgreSQL连接字符串
# 返回: 无
# 说明: 创建databasechangelog和databasechangeloglock表
#       用于记录数据库版本变更历史和锁定状态
#       兼容Liquibase数据库版本管理工具的表结构
#       使用IF NOT EXISTS确保表创建的幂等性
#-------------------------------------------------------------------------------
psqlCreateCvsTable() {
  local psql="$1"

  # 检查参数是否为空
  if [ -z "$psql" ]; then
    echo "[ERROR]: PostgreSQL连接字符串不能为空" >&2
    exit 1
  fi

  # 提取schema名称
  local schema
  schema=$(psqlGetSchema "$psql")
  if [ $? -ne 0 ] || [ -z "$schema" ]; then
    echo "[ERROR]: 无法从连接字符串中提取schema信息" >&2
    exit 1
  fi

  echo "[INFO]: 创建数据库版本管理表结构 (schema: $schema)" > /dev/tty

  # 先授予权限，然后创建版本管理表
  $psql << EOF
-- 授予当前用户对 schema 的权限
GRANT CREATE ON SCHEMA $schema TO CURRENT_USER;
GRANT USAGE ON SCHEMA $schema TO CURRENT_USER;
EOF

  # 创建版本管理表
  $psql << EOF
-- 创建数据库变更日志表
CREATE TABLE IF NOT EXISTS $schema."databasechangelog" (
  "id" varchar(255) NOT NULL,
  "author" varchar(255) NOT NULL,
  "filename" varchar(255),
  "dateexecuted" timestamp(6) NOT NULL,
  "orderexecuted" int4 NOT NULL,
  "exectype" varchar(10),
  "md5sum" varchar(35),
  "description" varchar(255),
  "comments" varchar(255),
  "tag" varchar(255),
  "liquibase" varchar(20),
  "contexts" varchar(255),
  "labels" varchar(255),
  "deployment_id" varchar(10)
);

-- 创建数据库变更锁定表
CREATE TABLE IF NOT EXISTS $schema."databasechangeloglock" (
  "id" int4 NOT NULL,
  "locked" bool NOT NULL,
  "lockgranted" timestamp(6),
  "lockedby" varchar(255),
  CONSTRAINT "databasechangeloglock_pkey" PRIMARY KEY ("id")
);

-- 初始化锁定表数据
INSERT INTO $schema."databasechangeloglock" ("id", "locked")
SELECT 1, false
WHERE NOT EXISTS (SELECT 1 FROM $schema."databasechangeloglock" WHERE "id" = 1);
EOF

  local create_result=$?
  if [ $create_result -eq 0 ]; then
    echo "[INFO]: 数据库版本管理表创建成功" > /dev/tty
  else
    echo "[ERROR]: 数据库版本管理表创建失败" >&2
    exit 1
  fi
}

#-------------------------------------------------------------------------------
# 函数名: psqlMarkVersion
# 功能: 在数据库中标记当前部署的版本号
# 参数:
#   $1: PostgreSQL连接字符串
#   $2: 版本号字符串
# 返回: 无
# 说明: 向databasechangelog表中插入版本记录
#       记录版本号、作者、执行时间等信息，用于版本追踪
#       使用当前时间戳作为执行时间
#-------------------------------------------------------------------------------
psqlMarkVersion() {
  local psql="$1"
  local version="$2"

  # 检查参数是否为空
  if [ -z "$psql" ]; then
    echo "[ERROR]: PostgreSQL连接字符串不能为空" >&2
    exit 1
  fi

  if [ -z "$version" ]; then
    echo "[ERROR]: 版本号不能为空" >&2
    exit 1
  fi

  # 提取schema名称
  local schema
  schema=$(psqlGetSchema "$psql")
  if [ $? -ne 0 ] || [ -z "$schema" ]; then
    echo "[ERROR]: 无法从连接字符串中提取schema信息" >&2
    exit 1
  fi

  # 生成当前时间戳
  local current_time
  current_time=$(date '+%Y-%m-%d %H:%M:%S')

  echo "[INFO]: 标记部署版本: $version" > /dev/tty

  # 插入版本记录
  local insert_sql="INSERT INTO $schema.databasechangelog(id, author, dateexecuted, orderexecuted, tag) VALUES('$version', 'compose_deploy', '$current_time', 1, '$version');"

  local result
  echo "[DEBUG]: $psql -c \"$insert_sql\"" >&2
  result=$($psql -c "$insert_sql" 2>&1)
  local insert_result=$?

  if [ $insert_result -eq 0 ]; then
    echo "[INFO]: 版本标记成功: $version" > /dev/tty
  else
    echo "[ERROR]: 版本标记失败: $result" >&2
    exit 1
  fi
}
