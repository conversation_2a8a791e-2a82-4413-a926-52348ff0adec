psqlGetCurVer: 用于获取当前部署的版本号。
psqlMarkVersion: 用于在数据库中标记新的部署版本号。
psqlCheckSchema: 用于检查数据库中是否存在指定的 schema。
psqlCreateCvsTable: 用于创建数据库版本管理所需的表。
psqlExecSqlFileWithDatabase: 用于在指定的数据库中执行 SQL 文件，并在需要时创建数据库和 schema。
psqlExecSqlFile: 用于在指定的数据库连接上执行 SQL 文件。
psqlCheckConn: 用于检查与 PostgreSQL 数据库的连接。
tdsqlImport: 用于导入 TDengine 的初始化 SQL 脚本。
tdsqlCheckInit: 用于检查 TDengine 数据库是否已成功初始化。
tdsqlCheckConn: 用于检查与 TDengine 数据库的连接。
