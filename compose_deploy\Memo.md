```bash
  # 数据库连接参数
  local conn_params=("${!1}")

  # 连接参数为空
  if [ -z $conn_params ]; then
    echo "[ERROR]: 连接参数数组未传入 " >&2
    return 11
  fi
  if [ ${#conn_params[@]} -eq 0 ]; then
    echo "[ERROR]: 连接参数数组不能为空" >&2
    return 12
  fi

  local sql_cmd
  # 参数：容器名、超级用户名、密码、数据库名（其他默认）
  sql_cmd=$(psqlCreateConn "${conn_params[0]}" "${conn_params[7]}" "${conn_params[2]}" "${conn_params[3]}" "${conn_params[4]}")
  conn_result="$?"
  if [ $conn_result -ne 0 ]; then
    echo_error "${conn_params[0]} 数据库连接获取失败置，请检查数据库配置"
    return 21
  fi
```

- psqlGetCurVer: 用于获取当前部署的版本号。
- psqlMarkVersion: 用于在数据库中标记新的部署版本号。
- psqlCheckSchema: 用于检查数据库中是否存在指定的 schema。
- psqlCreateCvsTable: 用于创建数据库版本管理所需的表。
- psqlExecSqlFileWithDatabase: 用于在指定的数据库中执行 SQL 文件，并在需要时创建数据库和 schema。
- psqlExecSqlFile: 用于在指定的数据库连接上执行 SQL 文件。
- psqlCheckConn: 用于检查与 PostgreSQL 数据库的连接。
------
tdsqlImport: 用于导入 TDengine 的初始化 SQL 脚本。
tdsqlCheckInit: 用于检查 TDengine 数据库是否已成功初始化。
tdsqlCheckConn: 用于检查与 TDengine 数据库的连接。

```sql
GRANT ALL PRIVILEGES ON DATABASE D TO U;
GRANT USAGE, CREATE ON SCHEMA S TO U;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA S TO U;


GRANT ALL PRIVILEGES ON DATABASE nacos TO postgres;
GRANT USAGE, CREATE ON SCHEMA public TO postgres;GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO postgres;
```
