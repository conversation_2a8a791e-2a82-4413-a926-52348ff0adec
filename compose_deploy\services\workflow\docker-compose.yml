# =============================================================================
# YAML 模板定义 (按功能分组)
# =============================================================================

# 基础服务模板
x-defaults: &defaults
  restart: unless-stopped
  logging:
    driver: "json-file"
    options:
      max-size: "1g"
      max-file: "2"
  networks:
    - middle

# Nginx 前端服务模板
x-nginx-defaults: &nginx-defaults
  <<: *defaults
  env_file:
    - ../base/.env
    - ../mes/.env
  healthcheck:
    test: ["CMD", "nginx", "-t"]
    interval: 30s
    timeout: 10s
    retries: 3
    start_period: 30s

# Nginx 工作流模块服务模板
x-nginx-reset: &nginx-reset
  <<: *nginx-defaults
  command: nginx -g 'daemon off;'
  volumes:
    - "../mos/config/nginx-default.conf:/etc/nginx/conf.d/default.conf:ro"

# 后端服务模板
x-backend-defaults: &backend-defaults
  <<: *defaults
  env_file:
    - ../base/.env
    - ../workflow/.env
    - ../mos/.env

# =============================================================================
# 网络定义
# =============================================================================

networks:
  # 中台统一网络 - 与其他服务共享网络环境
  middle:
    external: true
    name: ${DOCKER_NETWORK}

# =============================================================================
# 服务定义 (按工作流系统架构和依赖关系排序)
# =============================================================================

services:
  # ---------------------------------------------------------------------------
  # 后端服务层 - 工作流引擎核心服务
  # ---------------------------------------------------------------------------

  # 工作流后端服务 (流程引擎和业务逻辑)
  backend-workflow:
    <<: *backend-defaults
    image: harbor2.qdbdtd.com:8088/bjproduct/platform/bladex/bdtd-workflow-back:${BACKEND_WF_VERSION}
    container_name: workflow-backend-service
    environment:
      # MinIO 存储桶配置 (与 MES 系统共享)
      - MES_MINIO_BUCKETNAME=${MES_MINIO_BUCKETNAME}
      # MES 系统集成配置
      - MES_HOST=backend-mes-biz
      - MES_PORT=9001
    # ports:
    #   - "${PORT_WORKFLOW_BACKEND}:8004"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8004/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 90s

  # ---------------------------------------------------------------------------
  # 前端服务层 - 工作流用户界面服务
  # ---------------------------------------------------------------------------

  # 工作流前端服务 (流程设计器和管理界面)
  frontend-base-workflow_serve:
    <<: *nginx-reset
    image: harbor2.qdbdtd.com:8088/bjproduct/platform/front-services/bdtd-workflow-front:${FRONTEND_WF_VERSION}
    container_name: workflow-frontend-service
    # environment:
    #   - BACKEND_ENDPOINT=${BLADEX_BACKEND_ENDPOINT}
    depends_on:
      - backend-workflow
