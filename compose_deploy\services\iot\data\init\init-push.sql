-- drop database if exists push;
-- create database push;
-- \c push;

-- public.bdata_alert_indictor_id_seq definition
DROP SEQUENCE IF EXISTS public.bdata_alert_indictor_id_seq;
CREATE SEQUENCE public.bdata_alert_indictor_id_seq INCREMENT BY 1 MINVALUE 1 MAXVALUE 9223372036854775807 START 1 CACHE 1 NO CYCLE;
-- public.bdata_alert_indictor_type_id_seq definition
DROP SEQUENCE IF EXISTS public.bdata_alert_indictor_type_id_seq;
CREATE SEQUENCE public.bdata_alert_indictor_type_id_seq INCREMENT BY 1 MINVALUE 1 MAXVALUE 9223372036854775807 START 1 CACHE 1 NO CYCLE;
-- public.bdata_alert_push_mid_id_seq definition
DROP SEQUENCE IF EXISTS public.bdata_alert_push_mid_id_seq;
CREATE SEQUENCE public.bdata_alert_push_mid_id_seq INCREMENT BY 1 MINVALUE 1 MAXVALUE 9223372036854775807 START 1 CACHE 1 NO CYCLE;
-- public.bdata_rule_conf_id_seq definition
DROP SEQUENCE IF EXISTS public.bdata_rule_conf_id_seq;
CREATE SEQUENCE public.bdata_rule_conf_id_seq INCREMENT BY 1 MINVALUE 1 MAXVALUE 2147483647 START 1 CACHE 1 NO CYCLE;
-- public.bdata_rule_push_mid_id_seq definition
DROP SEQUENCE IF EXISTS public.bdata_rule_push_mid_id_seq;
CREATE SEQUENCE public.bdata_rule_push_mid_id_seq INCREMENT BY 1 MINVALUE 1 MAXVALUE 2147483647 START 1 CACHE 1 NO CYCLE;
-- public.channel_push_mode_seq definition
DROP SEQUENCE IF EXISTS public.channel_push_mode_seq;
CREATE SEQUENCE public.channel_push_mode_seq INCREMENT BY 1 MINVALUE 1 MAXVALUE 9223372036854775807 START 1 CACHE 1 NO CYCLE;
-- public.channel_push_type_seq definition
DROP SEQUENCE IF EXISTS public.channel_push_type_seq;
CREATE SEQUENCE public.channel_push_type_seq INCREMENT BY 1 MINVALUE 1 MAXVALUE 9223372036854775807 START 1 CACHE 1 NO CYCLE;
-- public.hazard_handle_employee_id_seq definition
DROP SEQUENCE IF EXISTS public.hazard_handle_employee_id_seq;
CREATE SEQUENCE public.hazard_handle_employee_id_seq INCREMENT BY 1 MINVALUE 1 MAXVALUE 2147483647 START 1 CACHE 1 NO CYCLE;
-- public.hazard_warning_alarm_handle_id_seq definition
DROP SEQUENCE IF EXISTS public.hazard_warning_alarm_handle_id_seq;
CREATE SEQUENCE public.hazard_warning_alarm_handle_id_seq INCREMENT BY 1 MINVALUE 1 MAXVALUE 2147483647 START 1 CACHE 1 NO CYCLE;
-- public.hazard_warning_alarm_info_id_seq definition
DROP SEQUENCE IF EXISTS public.hazard_warning_alarm_info_id_seq;
CREATE SEQUENCE public.hazard_warning_alarm_info_id_seq INCREMENT BY 1 MINVALUE 1 MAXVALUE 2147483647 START 1 CACHE 1 NO CYCLE;
-- public.hazard_warning_time_rule_id_seq definition
DROP SEQUENCE IF EXISTS public.hazard_warning_time_rule_id_seq;
CREATE SEQUENCE public.hazard_warning_time_rule_id_seq INCREMENT BY 1 MINVALUE 1 MAXVALUE 2147483647 START 1 CACHE 1 NO CYCLE;
-- public.indicator_system_relative_id_seq definition
DROP SEQUENCE IF EXISTS public.indicator_system_relative_id_seq;
CREATE SEQUENCE public.indicator_system_relative_id_seq INCREMENT BY 1 MINVALUE 1 MAXVALUE 9223372036854775807 START 1 CACHE 1 NO CYCLE;
-- public.push_channel_mode_employee_id_seq definition
DROP SEQUENCE IF EXISTS public.push_channel_mode_employee_id_seq;
CREATE SEQUENCE public.push_channel_mode_employee_id_seq INCREMENT BY 1 MINVALUE 1 MAXVALUE 9223372036854775807 START 1 CACHE 1 NO CYCLE;
-- public.push_detail_id_seq definition
DROP SEQUENCE IF EXISTS public.push_detail_id_seq;
CREATE SEQUENCE public.push_detail_id_seq INCREMENT BY 1 MINVALUE 1 MAXVALUE 9223372036854775807 START 1 CACHE 1 NO CYCLE;
-- public.push_history_seq definition
DROP SEQUENCE IF EXISTS public.push_history_seq;
CREATE SEQUENCE public.push_history_seq INCREMENT BY 1 MINVALUE 1 MAXVALUE 9223372036854775807 START 1 CACHE 1 NO CYCLE;
-- public.push_sms_id_seq definition
DROP SEQUENCE IF EXISTS public.push_sms_id_seq;
CREATE SEQUENCE public.push_sms_id_seq INCREMENT BY 1 MINVALUE 1 MAXVALUE 9223372036854775807 START 1 CACHE 1 NO CYCLE;
-- public.send_account_seq definition
DROP SEQUENCE IF EXISTS public.send_account_seq;
CREATE SEQUENCE public.send_account_seq INCREMENT BY 1 MINVALUE 1 MAXVALUE 9223372036854775807 START 1 CACHE 1 NO CYCLE;
-- public.user_account_seq definition
DROP SEQUENCE IF EXISTS public.user_account_seq;
CREATE SEQUENCE public.user_account_seq INCREMENT BY 1 MINVALUE 1 MAXVALUE 9223372036854775807 START 1 CACHE 1 NO CYCLE;
-- public.waring_rule_issue_id_seq definition
DROP SEQUENCE IF EXISTS public.waring_rule_issue_id_seq;
CREATE SEQUENCE public.waring_rule_issue_id_seq INCREMENT BY 1 MINVALUE 1 MAXVALUE 9223372036854775807 START 1 CACHE 1 NO CYCLE;
-- public.warning_alarm_info_id_seq definition
DROP SEQUENCE IF EXISTS public.warning_alarm_info_id_seq;
CREATE SEQUENCE public.warning_alarm_info_id_seq INCREMENT BY 1 MINVALUE 1 MAXVALUE 9223372036854775807 START 1 CACHE 1 NO CYCLE;
-- public.warning_channel_classify_id_seq definition
DROP SEQUENCE IF EXISTS public.warning_channel_classify_id_seq;
CREATE SEQUENCE public.warning_channel_classify_id_seq INCREMENT BY 1 MINVALUE 1 MAXVALUE 9223372036854775807 START 1 CACHE 1 NO CYCLE;
-- public.warning_time_rule_id_seq definition
DROP SEQUENCE IF EXISTS public.warning_time_rule_id_seq;
CREATE SEQUENCE public.warning_time_rule_id_seq INCREMENT BY 1 MINVALUE 1 MAXVALUE 2147483647 START 1 CACHE 1 NO CYCLE;
-------------
SELECT setval(
    '"public"."bdata_alert_indictor_id_seq"',
    258,
    true
  );
SELECT setval(
    '"public"."bdata_alert_indictor_type_id_seq"',
    89,
    true
  );
-- ----------------------------
-- Table structure for ai_model_system
-- ----------------------------
DROP TABLE IF EXISTS "public"."ai_model_system";
CREATE TABLE "public"."ai_model_system" (
  "id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "name" varchar(255) COLLATE "pg_catalog"."default"
);
COMMENT ON COLUMN "public"."ai_model_system"."id" IS '系统id';
COMMENT ON COLUMN "public"."ai_model_system"."name" IS '系统名称';
-- ----------------------------
-- Table structure for bdata_alert_indictor
-- ----------------------------
DROP TABLE IF EXISTS "public"."bdata_alert_indictor";
CREATE TABLE "public"."bdata_alert_indictor" (
  "id" int4 NOT NULL DEFAULT nextval('bdata_alert_indictor_id_seq'::regclass),
  "name" varchar(255) COLLATE "pg_catalog"."default",
  "type_id" int4,
  "active" bool,
  "indictor" varchar(255) COLLATE "pg_catalog"."default",
  "condition" varchar(255) COLLATE "pg_catalog"."default",
  "oper" varchar(255) COLLATE "pg_catalog"."default",
  "reminder" varchar(255) COLLATE "pg_catalog"."default",
  "sms" bool,
  "warningcreatetime" varchar(255) COLLATE "pg_catalog"."default",
  "channel_push_mode_id" int8,
  "oid" varchar COLLATE "pg_catalog"."default",
  "alg_config_id" int4,
  "camera_id" int4,
  "type" varchar COLLATE "pg_catalog"."default"
);
COMMENT ON COLUMN "public"."bdata_alert_indictor"."id" IS 'id';
COMMENT ON COLUMN "public"."bdata_alert_indictor"."name" IS '名称';
COMMENT ON COLUMN "public"."bdata_alert_indictor"."type_id" IS '指标类型';
COMMENT ON COLUMN "public"."bdata_alert_indictor"."active" IS '是否开启';
COMMENT ON COLUMN "public"."bdata_alert_indictor"."indictor" IS '指标';
COMMENT ON COLUMN "public"."bdata_alert_indictor"."condition" IS '判断条件';
COMMENT ON COLUMN "public"."bdata_alert_indictor"."oper" IS '处理措施';
COMMENT ON COLUMN "public"."bdata_alert_indictor"."reminder" IS '提醒目标人';
COMMENT ON COLUMN "public"."bdata_alert_indictor"."sms" IS '是否短信通知';
COMMENT ON COLUMN "public"."bdata_alert_indictor"."warningcreatetime" IS '报警最后触发时间';
COMMENT ON COLUMN "public"."bdata_alert_indictor"."channel_push_mode_id" IS '通知模板id';
COMMENT ON COLUMN "public"."bdata_alert_indictor"."alg_config_id" IS '算法配置id';
COMMENT ON COLUMN "public"."bdata_alert_indictor"."camera_id" IS 'AI--摄像头id';
COMMENT ON COLUMN "public"."bdata_alert_indictor"."type" IS '算法类型';
COMMENT ON TABLE "public"."bdata_alert_indictor" IS '报警指标库';
-- ----------------------------
-- Table structure for bdata_alert_indictor_type
-- ----------------------------
DROP TABLE IF EXISTS "public"."bdata_alert_indictor_type";
CREATE TABLE "public"."bdata_alert_indictor_type" (
  "id" int4 NOT NULL DEFAULT nextval('bdata_alert_indictor_type_id_seq'::regclass),
  "alert_type" varchar(255) COLLATE "pg_catalog"."default",
  "name" varchar(255) COLLATE "pg_catalog"."default",
  "pid" int4,
  "oid" varchar COLLATE "pg_catalog"."default"
);
COMMENT ON COLUMN "public"."bdata_alert_indictor_type"."id" IS 'id';
COMMENT ON COLUMN "public"."bdata_alert_indictor_type"."alert_type" IS '报警类型';
COMMENT ON COLUMN "public"."bdata_alert_indictor_type"."name" IS '名称';
COMMENT ON COLUMN "public"."bdata_alert_indictor_type"."pid" IS '父id';
COMMENT ON TABLE "public"."bdata_alert_indictor_type" IS '报警指标类型树';
-- ----------------------------
-- Table structure for bdata_alert_push_mid
-- ----------------------------
DROP TABLE IF EXISTS "public"."bdata_alert_push_mid";
CREATE TABLE "public"."bdata_alert_push_mid" (
  "id" int4 NOT NULL DEFAULT nextval('bdata_alert_push_mid_id_seq'::regclass),
  "alert_id" int4,
  "push_mode_id" int8
);
COMMENT ON COLUMN "public"."bdata_alert_push_mid"."id" IS '自增主键id';
COMMENT ON COLUMN "public"."bdata_alert_push_mid"."alert_id" IS '指标库id';
COMMENT ON COLUMN "public"."bdata_alert_push_mid"."push_mode_id" IS '通知模板id';
-- ----------------------------
-- Table structure for bdata_rule_conf
-- ----------------------------
DROP TABLE IF EXISTS "public"."bdata_rule_conf";
CREATE TABLE "public"."bdata_rule_conf" (
  "id" int4 NOT NULL DEFAULT nextval('bdata_rule_conf_id_seq'::regclass),
  "hazard_type" varchar(255) COLLATE "pg_catalog"."default",
  "point_type" varchar(255) COLLATE "pg_catalog"."default",
  "active" bool DEFAULT true,
  "red_warning" varchar(255) COLLATE "pg_catalog"."default",
  "orange_warning" varchar(255) COLLATE "pg_catalog"."default",
  "yellow_warning" varchar(255) COLLATE "pg_catalog"."default",
  "blue_warning" varchar(255) COLLATE "pg_catalog"."default",
  "system_name" varchar(255) COLLATE "pg_catalog"."default",
  "create_time" timestamp(6),
  "update_time" timestamp(6),
  "system_id" int4
);
COMMENT ON COLUMN "public"."bdata_rule_conf"."id" IS '主键id';
COMMENT ON COLUMN "public"."bdata_rule_conf"."hazard_type" IS '灾害类型';
COMMENT ON COLUMN "public"."bdata_rule_conf"."point_type" IS '测点类型';
COMMENT ON COLUMN "public"."bdata_rule_conf"."active" IS '是否开启';
COMMENT ON COLUMN "public"."bdata_rule_conf"."red_warning" IS '红色预警';
COMMENT ON COLUMN "public"."bdata_rule_conf"."orange_warning" IS '橙色预警';
COMMENT ON COLUMN "public"."bdata_rule_conf"."yellow_warning" IS '黄色预警';
COMMENT ON COLUMN "public"."bdata_rule_conf"."blue_warning" IS '蓝色预警';
COMMENT ON COLUMN "public"."bdata_rule_conf"."system_name" IS '所属系统';
COMMENT ON COLUMN "public"."bdata_rule_conf"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."bdata_rule_conf"."update_time" IS '更新时间';
COMMENT ON COLUMN "public"."bdata_rule_conf"."system_id" IS '系统编码';
COMMENT ON TABLE "public"."bdata_rule_conf" IS '预警报警指标配置(同指标)';
-- ----------------------------
-- Table structure for bdata_rule_push_mid
-- ----------------------------
DROP TABLE IF EXISTS "public"."bdata_rule_push_mid";
CREATE TABLE "public"."bdata_rule_push_mid" (
  "id" int4 NOT NULL DEFAULT nextval('bdata_rule_push_mid_id_seq'::regclass),
  "bdata_rule_id" int4,
  "push_mode_id" int8
);
COMMENT ON COLUMN "public"."bdata_rule_push_mid"."id" IS '自增主键id';
COMMENT ON COLUMN "public"."bdata_rule_push_mid"."bdata_rule_id" IS '指标库id';
COMMENT ON COLUMN "public"."bdata_rule_push_mid"."push_mode_id" IS '通知模板id';
-- ----------------------------
-- Table structure for channel_push_mode
-- ----------------------------
DROP TABLE IF EXISTS "public"."channel_push_mode";
CREATE TABLE "public"."channel_push_mode" (
  "id" int8 NOT NULL DEFAULT nextval('channel_push_mode_seq'::regclass),
  "push_types" varchar(10) COLLATE "pg_catalog"."default",
  "is_del" varchar(1) COLLATE "pg_catalog"."default",
  "create_time" varchar(50) COLLATE "pg_catalog"."default",
  "message" varchar(500) COLLATE "pg_catalog"."default",
  "send_users" varchar(255) COLLATE "pg_catalog"."default",
  "send_groups" varchar(255) COLLATE "pg_catalog"."default",
  "cc_users" varchar(255) COLLATE "pg_catalog"."default",
  "cc_groups" varchar(255) COLLATE "pg_catalog"."default",
  "send_delay" varchar(255) COLLATE "pg_catalog"."default",
  "cc_delay" varchar(255) COLLATE "pg_catalog"."default",
  "send_users_name" varchar(1000) COLLATE "pg_catalog"."default",
  "send_groups_name" varchar(1000) COLLATE "pg_catalog"."default",
  "cc_users_name" varchar(1000) COLLATE "pg_catalog"."default",
  "cc_groups_name" varchar(1000) COLLATE "pg_catalog"."default",
  "send_type" varchar(255) COLLATE "pg_catalog"."default",
  "update_time" varchar(50) COLLATE "pg_catalog"."default",
  "create_user_code" int8,
  "create_user_name" varchar(255) COLLATE "pg_catalog"."default",
  "native_phones" varchar(500) COLLATE "pg_catalog"."default",
  "config_json" varchar(500) COLLATE "pg_catalog"."default",
  "channel_code" varchar(10) COLLATE "pg_catalog"."default",
  "push_code" varchar(10) COLLATE "pg_catalog"."default"
);
COMMENT ON COLUMN "public"."channel_push_mode"."push_types" IS '推送类型：1-通知；2-短信；3-邮件';
COMMENT ON COLUMN "public"."channel_push_mode"."send_type" IS '发送方式1--及时发送 2--定时发送 3--未处理后发送';
COMMENT ON COLUMN "public"."channel_push_mode"."create_user_code" IS '创建人员工号';
COMMENT ON COLUMN "public"."channel_push_mode"."create_user_name" IS '创建人姓名';
COMMENT ON COLUMN "public"."channel_push_mode"."native_phones" IS '本地手机号';
COMMENT ON COLUMN "public"."channel_push_mode"."config_json" IS '发送方账号主体配置信息 相关项结构化数据';
COMMENT ON COLUMN "public"."channel_push_mode"."channel_code" IS '推送 的 厂商信息表 中的代码 短信:1--联通短信；2--短信机；3--融合通信；4--阿里云短信；通知：1--app推送 极光推送；2--业务(app/pc通知)';
COMMENT ON COLUMN "public"."channel_push_mode"."push_code" IS '推送 的 推送方式 中的代码';
-- ----------------------------
-- Table structure for channel_push_type
-- ----------------------------
DROP TABLE IF EXISTS "public"."channel_push_type";
CREATE TABLE "public"."channel_push_type" (
  "id" int8 NOT NULL DEFAULT nextval('channel_push_type_seq'::regclass),
  "chcode_a" varchar(100) COLLATE "pg_catalog"."default",
  "chcode_b" varchar(100) COLLATE "pg_catalog"."default",
  "is_push_flag" varchar(1) COLLATE "pg_catalog"."default",
  "is_del" varchar(1) COLLATE "pg_catalog"."default",
  "create_time" varchar(50) COLLATE "pg_catalog"."default",
  "message" varchar(50) COLLATE "pg_catalog"."default",
  "push_type" int8
);
COMMENT ON COLUMN "public"."channel_push_type"."id" IS '记录ID';
COMMENT ON COLUMN "public"."channel_push_type"."chcode_a" IS '标准模版';
COMMENT ON COLUMN "public"."channel_push_type"."chcode_b" IS '对应中文模版';
COMMENT ON COLUMN "public"."channel_push_type"."is_push_flag" IS '该渠道有无该推送方式的权限';
COMMENT ON COLUMN "public"."channel_push_type"."is_del" IS '是否删除';
COMMENT ON COLUMN "public"."channel_push_type"."create_time" IS '创建日期';
COMMENT ON COLUMN "public"."channel_push_type"."message" IS '备注';
COMMENT ON COLUMN "public"."channel_push_type"."push_type" IS '推送类型';
COMMENT ON TABLE "public"."channel_push_type" IS '厂商推送方式字典表  管理厂家的推送方式配置';
-- ----------------------------
-- Table structure for gis_warning_alarm_info
-- ----------------------------
DROP TABLE IF EXISTS "public"."gis_warning_alarm_info";
CREATE TABLE "public"."gis_warning_alarm_info" (
  "warning_alarm_info_id" int4 NOT NULL DEFAULT nextval('warning_alarm_info_id_seq'::regclass),
  "warning_alarm_info_param" text COLLATE "pg_catalog"."default",
  "warning_alarm_info_createtime" varchar(30) COLLATE "pg_catalog"."default",
  "warning_alarm_info_status" int2 DEFAULT 0,
  "warning_alarm_id" int4,
  "warning_alarm_info_content" varchar(255) COLLATE "pg_catalog"."default",
  "user_id" int4,
  "warning_alarm_info_show" int2 DEFAULT 1,
  "warning_alarm_info_handle" int2 DEFAULT 0,
  "warning_alarm_info_updatetime" varchar(30) COLLATE "pg_catalog"."default",
  "warning_alarm_info_type" int2,
  "warning_alarm_info_level" varchar(255) COLLATE "pg_catalog"."default",
  "message_template_id" int4,
  "warning_alarm_info_describe" varchar(255) COLLATE "pg_catalog"."default",
  "warning_alarm_source_system_code" varchar(255) COLLATE "pg_catalog"."default",
  "warning_alarm_source_id" int4,
  "update_time" timestamp(6) DEFAULT now(),
  "type" varchar(255) COLLATE "pg_catalog"."default",
  "point_ids" text COLLATE "pg_catalog"."default",
  "camera_id" int4,
  "camera_name" varchar(255) COLLATE "pg_catalog"."default",
  "alg_id" int4,
  "alg_name" varchar(255) COLLATE "pg_catalog"."default",
  "level" int4,
  "alarm_type" int4,
  "handle_status" int4,
  "handle_advice" text COLLATE "pg_catalog"."default",
  "location" varchar COLLATE "pg_catalog"."default",
  "image_path" text COLLATE "pg_catalog"."default",
  "system_type" varchar COLLATE "pg_catalog"."default",
  "video_path" text COLLATE "pg_catalog"."default",
  "system_name" varchar COLLATE "pg_catalog"."default",
  "system_id" varchar COLLATE "pg_catalog"."default",
  "point_id" varchar COLLATE "pg_catalog"."default",
  "value" varchar COLLATE "pg_catalog"."default",
  "def_unit" varchar COLLATE "pg_catalog"."default",
  "min" varchar COLLATE "pg_catalog"."default",
  "max" varchar COLLATE "pg_catalog"."default",
  "equality" varchar COLLATE "pg_catalog"."default",
  "sensor_name" varchar COLLATE "pg_catalog"."default",
  "display_name" varchar COLLATE "pg_catalog"."default",
  "alarm_distance_d_t_o_list" text COLLATE "pg_catalog"."default",
  "behavior_type" int2,
  "extra" text COLLATE "pg_catalog"."default",
  "alg_real_name" varchar COLLATE "pg_catalog"."default"
);
COMMENT ON COLUMN "public"."gis_warning_alarm_info"."warning_alarm_info_param" IS '信息参输';
COMMENT ON COLUMN "public"."gis_warning_alarm_info"."warning_alarm_info_createtime" IS '创建时间';
COMMENT ON COLUMN "public"."gis_warning_alarm_info"."warning_alarm_info_status" IS '是否推消息状态 0 未成功 1已成功';
COMMENT ON COLUMN "public"."gis_warning_alarm_info"."warning_alarm_id" IS '报警预警id';
COMMENT ON COLUMN "public"."gis_warning_alarm_info"."warning_alarm_info_content" IS '处理结果';
COMMENT ON COLUMN "public"."gis_warning_alarm_info"."user_id" IS '用户id';
COMMENT ON COLUMN "public"."gis_warning_alarm_info"."warning_alarm_info_show" IS '实现显示到实时统计 默认是1 显示0 不显示';
COMMENT ON COLUMN "public"."gis_warning_alarm_info"."warning_alarm_info_handle" IS '是否处理 默认是0 没处理 1已处理';
COMMENT ON COLUMN "public"."gis_warning_alarm_info"."warning_alarm_info_updatetime" IS '修改时间';
COMMENT ON COLUMN "public"."gis_warning_alarm_info"."warning_alarm_info_type" IS '报警类型 1 预警 2 报警';
COMMENT ON COLUMN "public"."gis_warning_alarm_info"."warning_alarm_info_level" IS '级别';
COMMENT ON COLUMN "public"."gis_warning_alarm_info"."message_template_id" IS '消息模板id';
COMMENT ON COLUMN "public"."gis_warning_alarm_info"."warning_alarm_info_describe" IS '描述';
COMMENT ON COLUMN "public"."gis_warning_alarm_info"."warning_alarm_source_system_code" IS '对应系统编码';
COMMENT ON COLUMN "public"."gis_warning_alarm_info"."warning_alarm_source_id" IS '系统对应id';
COMMENT ON COLUMN "public"."gis_warning_alarm_info"."update_time" IS '更新时间';
COMMENT ON COLUMN "public"."gis_warning_alarm_info"."type" IS '类型';
COMMENT ON COLUMN "public"."gis_warning_alarm_info"."point_ids" IS '站点名';
COMMENT ON COLUMN "public"."gis_warning_alarm_info"."camera_id" IS '摄像头id';
COMMENT ON COLUMN "public"."gis_warning_alarm_info"."camera_name" IS '摄像机名称';
COMMENT ON COLUMN "public"."gis_warning_alarm_info"."alg_id" IS '算法id';
COMMENT ON COLUMN "public"."gis_warning_alarm_info"."alg_name" IS '算法名称(分类名称)';
COMMENT ON COLUMN "public"."gis_warning_alarm_info"."level" IS 'AI隐患级别';
COMMENT ON COLUMN "public"."gis_warning_alarm_info"."alarm_type" IS '报警类型 1--AI预警报警';
COMMENT ON COLUMN "public"."gis_warning_alarm_info"."handle_status" IS '处理状态 0--未处理  1--已处理';
COMMENT ON COLUMN "public"."gis_warning_alarm_info"."handle_advice" IS '处理意见';
COMMENT ON COLUMN "public"."gis_warning_alarm_info"."location" IS 'AI位置--区域名称';
COMMENT ON COLUMN "public"."gis_warning_alarm_info"."image_path" IS 'AI--image路径';
COMMENT ON COLUMN "public"."gis_warning_alarm_info"."system_type" IS '系统类型';
COMMENT ON COLUMN "public"."gis_warning_alarm_info"."video_path" IS 'AI--视频路径';
COMMENT ON COLUMN "public"."gis_warning_alarm_info"."system_name" IS '系统名称';
COMMENT ON COLUMN "public"."gis_warning_alarm_info"."system_id" IS '系统编码';
COMMENT ON COLUMN "public"."gis_warning_alarm_info"."point_id" IS '传感器编号';
COMMENT ON COLUMN "public"."gis_warning_alarm_info"."value" IS '监测值';
COMMENT ON COLUMN "public"."gis_warning_alarm_info"."def_unit" IS '监测值--单位';
COMMENT ON COLUMN "public"."gis_warning_alarm_info"."min" IS '阈值--最小值';
COMMENT ON COLUMN "public"."gis_warning_alarm_info"."max" IS '阈值--最大值';
COMMENT ON COLUMN "public"."gis_warning_alarm_info"."equality" IS '阈值--固定值';
COMMENT ON COLUMN "public"."gis_warning_alarm_info"."sensor_name" IS '传感器类型名称';
COMMENT ON COLUMN "public"."gis_warning_alarm_info"."display_name" IS '摄像机别名';
COMMENT ON COLUMN "public"."gis_warning_alarm_info"."alarm_distance_d_t_o_list" IS '附近 人或车列表';
COMMENT ON COLUMN "public"."gis_warning_alarm_info"."behavior_type" IS '行为类型(0是人 1是物 2是车)';
COMMENT ON COLUMN "public"."gis_warning_alarm_info"."extra" IS 'AI统计字段';
COMMENT ON COLUMN "public"."gis_warning_alarm_info"."alg_real_name" IS '算法名称(中文)';
COMMENT ON TABLE "public"."gis_warning_alarm_info" IS '预警报警详情表';
-- ----------------------------
-- Table structure for hazard_handle_employee
-- ----------------------------
DROP TABLE IF EXISTS "public"."hazard_handle_employee";
CREATE TABLE "public"."hazard_handle_employee" (
  "id" int4 NOT NULL DEFAULT nextval('hazard_handle_employee_id_seq'::regclass),
  "employee_name" varchar(255) COLLATE "pg_catalog"."default",
  "phone" varchar(255) COLLATE "pg_catalog"."default",
  "post_name" varchar(255) COLLATE "pg_catalog"."default",
  "handle_id" int4
);
COMMENT ON COLUMN "public"."hazard_handle_employee"."employee_name" IS '员工姓名';
COMMENT ON COLUMN "public"."hazard_handle_employee"."phone" IS '手机号码';
COMMENT ON COLUMN "public"."hazard_handle_employee"."post_name" IS '岗位名称';
COMMENT ON COLUMN "public"."hazard_handle_employee"."handle_id" IS '预警报警处理id';
-- ----------------------------
-- Table structure for hazard_warning_alarm_handle
-- ----------------------------
DROP TABLE IF EXISTS "public"."hazard_warning_alarm_handle";
CREATE TABLE "public"."hazard_warning_alarm_handle" (
  "id" int4 NOT NULL DEFAULT nextval('hazard_warning_alarm_handle_id_seq'::regclass),
  "hazard_warning_alarm_info_id" int4,
  "handle_person" varchar(255) COLLATE "pg_catalog"."default",
  "handle_time" varchar(255) COLLATE "pg_catalog"."default",
  "handle_operation" text COLLATE "pg_catalog"."default"
);
COMMENT ON COLUMN "public"."hazard_warning_alarm_handle"."hazard_warning_alarm_info_id" IS '报警id';
COMMENT ON COLUMN "public"."hazard_warning_alarm_handle"."handle_person" IS '处理人';
COMMENT ON COLUMN "public"."hazard_warning_alarm_handle"."handle_time" IS '处理时间';
COMMENT ON COLUMN "public"."hazard_warning_alarm_handle"."handle_operation" IS '采取措施';
-- ----------------------------
-- Table structure for hazard_warning_alarm_info
-- ----------------------------
DROP TABLE IF EXISTS "public"."hazard_warning_alarm_info";
CREATE TABLE "public"."hazard_warning_alarm_info" (
  "id" int4 NOT NULL DEFAULT nextval('hazard_warning_alarm_info_id_seq'::regclass),
  "hazard_type" varchar(255) COLLATE "pg_catalog"."default",
  "point_type" varchar(255) COLLATE "pg_catalog"."default",
  "active" bool DEFAULT true,
  "system_name" varchar(255) COLLATE "pg_catalog"."default",
  "create_time" timestamp(6),
  "update_time" timestamp(6),
  "warning_level" varchar COLLATE "pg_catalog"."default",
  "warning_content" text COLLATE "pg_catalog"."default",
  "handle_status" int2 DEFAULT 0,
  "warning_status" int2 DEFAULT 0,
  "bdata_rule_id" int4,
  "point_id" varchar COLLATE "pg_catalog"."default",
  "value" varchar COLLATE "pg_catalog"."default",
  "system_id" int4,
  "handle_operation" varchar COLLATE "pg_catalog"."default",
  "handle_persons" varchar COLLATE "pg_catalog"."default"
);
COMMENT ON COLUMN "public"."hazard_warning_alarm_info"."id" IS '主键id';
COMMENT ON COLUMN "public"."hazard_warning_alarm_info"."hazard_type" IS '灾害类型';
COMMENT ON COLUMN "public"."hazard_warning_alarm_info"."point_type" IS '测点类型';
COMMENT ON COLUMN "public"."hazard_warning_alarm_info"."active" IS '是否开启';
COMMENT ON COLUMN "public"."hazard_warning_alarm_info"."system_name" IS '所属系统';
COMMENT ON COLUMN "public"."hazard_warning_alarm_info"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."hazard_warning_alarm_info"."update_time" IS '更新时间';
COMMENT ON COLUMN "public"."hazard_warning_alarm_info"."warning_level" IS '预警级别';
COMMENT ON COLUMN "public"."hazard_warning_alarm_info"."warning_content" IS '预警信息';
COMMENT ON COLUMN "public"."hazard_warning_alarm_info"."handle_status" IS '处理状态 0--未处理  1--已处理';
COMMENT ON COLUMN "public"."hazard_warning_alarm_info"."warning_status" IS '状态 0--未停止  1--已停止';
COMMENT ON COLUMN "public"."hazard_warning_alarm_info"."bdata_rule_id" IS '灾害配置id';
COMMENT ON COLUMN "public"."hazard_warning_alarm_info"."point_id" IS '点位';
COMMENT ON COLUMN "public"."hazard_warning_alarm_info"."value" IS '监测值';
COMMENT ON COLUMN "public"."hazard_warning_alarm_info"."system_id" IS '系统id';
COMMENT ON COLUMN "public"."hazard_warning_alarm_info"."handle_operation" IS '处置措施';
COMMENT ON COLUMN "public"."hazard_warning_alarm_info"."handle_persons" IS '处置人';
COMMENT ON TABLE "public"."hazard_warning_alarm_info" IS '灾害类型预警数据表';
-- ----------------------------
-- Table structure for hazard_warning_time_rule
-- ----------------------------
DROP TABLE IF EXISTS "public"."hazard_warning_time_rule";
CREATE TABLE "public"."hazard_warning_time_rule" (
  "id" int4 NOT NULL DEFAULT nextval('hazard_warning_time_rule_id_seq'::regclass),
  "time_rule" int4,
  "condition" varchar(255) COLLATE "pg_catalog"."default",
  "condition_value" int8,
  "bdata_rule_id" int4,
  "unit" varchar(255) COLLATE "pg_catalog"."default",
  "unit_value" int8
);
COMMENT ON COLUMN "public"."hazard_warning_time_rule"."time_rule" IS '规则';
COMMENT ON COLUMN "public"."hazard_warning_time_rule"."condition" IS '条件';
COMMENT ON COLUMN "public"."hazard_warning_time_rule"."condition_value" IS '数值  单位ms(毫秒)';
COMMENT ON COLUMN "public"."hazard_warning_time_rule"."bdata_rule_id" IS '指标id';
COMMENT ON COLUMN "public"."hazard_warning_time_rule"."unit" IS '单位';
COMMENT ON COLUMN "public"."hazard_warning_time_rule"."unit_value" IS '单位值';
-- ----------------------------
-- Table structure for indicator_system_relative
-- ----------------------------
DROP TABLE IF EXISTS "public"."indicator_system_relative";
CREATE TABLE "public"."indicator_system_relative" (
  "id" int4 NOT NULL DEFAULT nextval('indicator_system_relative_id_seq'::regclass),
  "indicator_id" int4,
  "system_id" int4,
  "system_name" varchar(255) COLLATE "pg_catalog"."default",
  "type_code" int4
);
COMMENT ON COLUMN "public"."indicator_system_relative"."id" IS '主键';
COMMENT ON COLUMN "public"."indicator_system_relative"."indicator_id" IS '指标id';
COMMENT ON COLUMN "public"."indicator_system_relative"."system_id" IS '系统id';
COMMENT ON COLUMN "public"."indicator_system_relative"."system_name" IS '系统名称';
COMMENT ON COLUMN "public"."indicator_system_relative"."type_code" IS '类型代码 1--data预警报警，2--AI预警报警';
-- ----------------------------
-- Table structure for push_channel_mode_employee
-- ----------------------------
DROP TABLE IF EXISTS "public"."push_channel_mode_employee";
CREATE TABLE "public"."push_channel_mode_employee" (
  "id" int4 NOT NULL DEFAULT nextval('push_channel_mode_employee_id_seq'::regclass),
  "employee_id" int8,
  "dept_id" int8,
  "dept_name" varchar(255) COLLATE "pg_catalog"."default",
  "post_code" varchar(255) COLLATE "pg_catalog"."default",
  "post_name" varchar(255) COLLATE "pg_catalog"."default",
  "employee_name" varchar(255) COLLATE "pg_catalog"."default",
  "phone" varchar(255) COLLATE "pg_catalog"."default",
  "push_mode_id" int8,
  "employee_type" varchar(255) COLLATE "pg_catalog"."default",
  "email" varchar(255) COLLATE "pg_catalog"."default",
  "email_title" varchar(255) COLLATE "pg_catalog"."default"
);
COMMENT ON COLUMN "public"."push_channel_mode_employee"."id" IS '主键自增id';
COMMENT ON COLUMN "public"."push_channel_mode_employee"."employee_id" IS '员工编号';
COMMENT ON COLUMN "public"."push_channel_mode_employee"."dept_id" IS '部门编号';
COMMENT ON COLUMN "public"."push_channel_mode_employee"."dept_name" IS '部门名称';
COMMENT ON COLUMN "public"."push_channel_mode_employee"."post_code" IS '岗位编号';
COMMENT ON COLUMN "public"."push_channel_mode_employee"."post_name" IS '岗位名称';
COMMENT ON COLUMN "public"."push_channel_mode_employee"."employee_name" IS '员工姓名';
COMMENT ON COLUMN "public"."push_channel_mode_employee"."phone" IS '手机号码';
COMMENT ON COLUMN "public"."push_channel_mode_employee"."push_mode_id" IS '推送模板id';
COMMENT ON COLUMN "public"."push_channel_mode_employee"."employee_type" IS '0--接收人，1--抄送人';
COMMENT ON COLUMN "public"."push_channel_mode_employee"."email" IS '邮箱';
COMMENT ON COLUMN "public"."push_channel_mode_employee"."email_title" IS '邮件_标题';
-- ----------------------------
-- Table structure for push_detail
-- ----------------------------
DROP TABLE IF EXISTS "public"."push_detail";
CREATE TABLE "public"."push_detail" (
  "id" int8 NOT NULL DEFAULT nextval('push_detail_id_seq'::regclass),
  "user_id" int8,
  "is_send" varchar(255) COLLATE "pg_catalog"."default",
  "push_content" text COLLATE "pg_catalog"."default",
  "create_time" timestamp(6),
  "push_mode_id" int8,
  "push_reply" varchar(255) COLLATE "pg_catalog"."default",
  "push_user_id" int8,
  "push_jump_code" varchar(255) COLLATE "pg_catalog"."default",
  "push_jump_param" varchar(255) COLLATE "pg_catalog"."default",
  "message_type" varchar(255) COLLATE "pg_catalog"."default",
  "message_level" varchar(255) COLLATE "pg_catalog"."default",
  "push_title" varchar(255) COLLATE "pg_catalog"."default",
  "push_user_name" varchar(255) COLLATE "pg_catalog"."default",
  "remind_type" varchar(255) COLLATE "pg_catalog"."default",
  "update_time" timestamp(6),
  "pc_url" varchar(255) COLLATE "pg_catalog"."default",
  "ph_page_type" varchar(255) COLLATE "pg_catalog"."default",
  "ph_url" varchar(255) COLLATE "pg_catalog"."default",
  "count_url" varchar(255) COLLATE "pg_catalog"."default",
  "push_type" varchar(255) COLLATE "pg_catalog"."default",
  "group_pc_url" varchar(255) COLLATE "pg_catalog"."default",
  "group_ph_url" varchar(255) COLLATE "pg_catalog"."default"
);
COMMENT ON COLUMN "public"."push_detail"."id" IS '记录ID';
COMMENT ON COLUMN "public"."push_detail"."user_id" IS '用户ID';
COMMENT ON COLUMN "public"."push_detail"."is_send" IS '是否已发送，0--未发送，1--已发送';
COMMENT ON COLUMN "public"."push_detail"."push_content" IS '推送内容';
COMMENT ON COLUMN "public"."push_detail"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."push_detail"."push_mode_id" IS '推送ID';
COMMENT ON COLUMN "public"."push_detail"."push_reply" IS '回复';
COMMENT ON COLUMN "public"."push_detail"."push_user_id" IS '回复用户';
COMMENT ON COLUMN "public"."push_detail"."push_jump_code" IS '跳转路由';
COMMENT ON COLUMN "public"."push_detail"."push_jump_param" IS '跳转参数';
COMMENT ON COLUMN "public"."push_detail"."message_type" IS '消息类型';
COMMENT ON COLUMN "public"."push_detail"."message_level" IS '消息级别';
COMMENT ON COLUMN "public"."push_detail"."push_title" IS '标题';
COMMENT ON COLUMN "public"."push_detail"."push_user_name" IS '读取人姓名';
COMMENT ON COLUMN "public"."push_detail"."remind_type" IS ' 提醒方式 0000：不需要任何提醒； 1000：需要短信提醒； 1100：需要短信和邮箱提醒。';
COMMENT ON COLUMN "public"."push_detail"."update_time" IS '更新时间';
COMMENT ON COLUMN "public"."push_detail"."pc_url" IS '消息详情 PC 端 url';
COMMENT ON COLUMN "public"."push_detail"."ph_page_type" IS '消息移动端页面类型（0：淮河能源 APP 内部原 生页面，1：H5 界面(code 登录认证) ，2：H5 界面(无登录认证) ，3： H5 界面(票据登录认证）。），可传空，默认是 3';
COMMENT ON COLUMN "public"."push_detail"."ph_url" IS '消息详情移动端 url';
COMMENT ON COLUMN "public"."push_detail"."count_url" IS '点击消息总数需要跳转到业务系统某个页面的 url';
COMMENT ON COLUMN "public"."push_detail"."push_type" IS '推送方式 1--通知  2--短信 3--通知+短信';
COMMENT ON COLUMN "public"."push_detail"."group_pc_url" IS '集团推送PC端url';
COMMENT ON COLUMN "public"."push_detail"."group_ph_url" IS '集团推送移动端url';
-- ----------------------------
-- Table structure for push_history
-- ----------------------------
DROP TABLE IF EXISTS "public"."push_history";
CREATE TABLE "public"."push_history" (
  "id" int8 NOT NULL DEFAULT nextval('push_history_seq'::regclass),
  "user_id" int8,
  "push_json" varchar(2000) COLLATE "pg_catalog"."default",
  "channel_code" varchar(1) COLLATE "pg_catalog"."default",
  "push_code" varchar(1) COLLATE "pg_catalog"."default",
  "is_full" varchar(1) COLLATE "pg_catalog"."default",
  "count" varchar(1) COLLATE "pg_catalog"."default",
  "full_message" varchar(2000) COLLATE "pg_catalog"."default",
  "is_del" varchar(1) COLLATE "pg_catalog"."default",
  "create_time" varchar(50) COLLATE "pg_catalog"."default",
  "message" varchar(50) COLLATE "pg_catalog"."default",
  "group_id" int8,
  "is_handle" varchar(1) COLLATE "pg_catalog"."default",
  "is_read" varchar(1) COLLATE "pg_catalog"."default",
  "phone" varchar(255) COLLATE "pg_catalog"."default",
  "email" varchar(255) COLLATE "pg_catalog"."default",
  "detail_id" int8,
  "type" varchar(1) COLLATE "pg_catalog"."default",
  "update_time" varchar(50) COLLATE "pg_catalog"."default",
  "read_time" varchar(50) COLLATE "pg_catalog"."default",
  "gis_warning_alarm_time" varchar(50) COLLATE "pg_catalog"."default",
  "warning_alarm_info_id" int4
);
COMMENT ON COLUMN "public"."push_history"."id" IS '记录ID';
COMMENT ON COLUMN "public"."push_history"."user_id" IS '用户ID';
COMMENT ON COLUMN "public"."push_history"."push_json" IS ' 相关项结构化数据';
COMMENT ON COLUMN "public"."push_history"."channel_code" IS '推送 的 厂商信息表 中的代码';
COMMENT ON COLUMN "public"."push_history"."push_code" IS '推送 的 推送方式 中的代码';
COMMENT ON COLUMN "public"."push_history"."is_full" IS '是否成功 1 是 0 不是';
COMMENT ON COLUMN "public"."push_history"."count" IS '失败计数  默认3次';
COMMENT ON COLUMN "public"."push_history"."full_message" IS '失败描述  "厂家返回的失败描述';
COMMENT ON COLUMN "public"."push_history"."is_del" IS '是否删除';
COMMENT ON COLUMN "public"."push_history"."create_time" IS '创建日期';
COMMENT ON COLUMN "public"."push_history"."message" IS '备注';
COMMENT ON COLUMN "public"."push_history"."group_id" IS '分组id';
COMMENT ON COLUMN "public"."push_history"."is_handle" IS '是否处理，0--未处理，1--已处理 2--处理中';
COMMENT ON COLUMN "public"."push_history"."is_read" IS '是否已读，0--未读，1--已读';
COMMENT ON COLUMN "public"."push_history"."phone" IS '手机号';
COMMENT ON COLUMN "public"."push_history"."email" IS '邮箱';
COMMENT ON COLUMN "public"."push_history"."detail_id" IS 'detail内容表id';
COMMENT ON COLUMN "public"."push_history"."type" IS '用户类型，0--发送人，1--抄送人';
COMMENT ON COLUMN "public"."push_history"."update_time" IS '更新时间';
COMMENT ON COLUMN "public"."push_history"."read_time" IS '读取时间';
COMMENT ON COLUMN "public"."push_history"."gis_warning_alarm_time" IS '预警报警时间';
COMMENT ON COLUMN "public"."push_history"."warning_alarm_info_id" IS '预警报警id';
COMMENT ON TABLE "public"."push_history" IS '推送信息历史表 用于描述 推送信息历史 那个用户什么时候推送过什么数据 推送的中心化结构数据为什么  是否错误 错误重试计数为什么 返回错误数据是什么';
-- ----------------------------
-- Table structure for push_sms
-- ----------------------------
DROP TABLE IF EXISTS "public"."push_sms";
CREATE TABLE "public"."push_sms" (
  "id" int8 NOT NULL DEFAULT nextval('push_sms_id_seq'::regclass),
  "content" varchar(500) COLLATE "pg_catalog"."default",
  "phone" varchar(500) COLLATE "pg_catalog"."default",
  "create_time" varchar(255) COLLATE "pg_catalog"."default",
  "channel_code" varchar(2) COLLATE "pg_catalog"."default",
  "push_code" varchar(2) COLLATE "pg_catalog"."default"
);
COMMENT ON COLUMN "public"."push_sms"."id" IS '主键自增';
COMMENT ON COLUMN "public"."push_sms"."content" IS '短信内容';
COMMENT ON COLUMN "public"."push_sms"."phone" IS '发送手机号';
COMMENT ON COLUMN "public"."push_sms"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."push_sms"."channel_code" IS '"A";  /// 联通
"B";  /// 短信机
"C";  /// 业务内部通知
"D";  /// 通知--极光推送
"E";  /// 融合推送
"F";  /// 阿里云    ';
COMMENT ON COLUMN "public"."push_sms"."push_code" IS '"A";  /// 短信
"B";  /// 邮件
"C";  /// 业务通知
"D";  /// 通知--极光推送 ';
-- ----------------------------
-- Table structure for send_account
-- ----------------------------
DROP TABLE IF EXISTS "public"."send_account";
CREATE TABLE "public"."send_account" (
  "id" int8 NOT NULL DEFAULT nextval('send_account_seq'::regclass),
  "config_json" varchar(500) COLLATE "pg_catalog"."default",
  "channel_code" varchar(1) COLLATE "pg_catalog"."default",
  "is_del" varchar(1) COLLATE "pg_catalog"."default",
  "create_time" varchar(50) COLLATE "pg_catalog"."default",
  "message" varchar(50) COLLATE "pg_catalog"."default"
);
COMMENT ON COLUMN "public"."send_account"."id" IS '记录ID';
COMMENT ON COLUMN "public"."send_account"."config_json" IS '发送方账号主体配置信息 相关项结构化数据';
COMMENT ON COLUMN "public"."send_account"."channel_code" IS '推送 的 厂商信息表 中的代码';
COMMENT ON COLUMN "public"."send_account"."is_del" IS '是否删除';
COMMENT ON COLUMN "public"."send_account"."create_time" IS '创建日期';
COMMENT ON COLUMN "public"."send_account"."message" IS '备注';
COMMENT ON TABLE "public"."send_account" IS '发送方账号信息表 用于存储或者配置发送方商户账号信息体';
-- ----------------------------
-- Table structure for system_duration
-- ----------------------------
DROP TABLE IF EXISTS "public"."system_duration";
CREATE TABLE "public"."system_duration" (
  "id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "name" varchar(255) COLLATE "pg_catalog"."default",
  "duration" int8
);
COMMENT ON COLUMN "public"."system_duration"."id" IS '主键';
-- ----------------------------
-- Table structure for user_account
-- ----------------------------
DROP TABLE IF EXISTS "public"."user_account";
CREATE TABLE "public"."user_account" (
  "id" int8 NOT NULL DEFAULT nextval('user_account_seq'::regclass),
  "user_id" int4,
  "oss_id" int4,
  "config_json" varchar(500) COLLATE "pg_catalog"."default",
  "channel_code" varchar(1) COLLATE "pg_catalog"."default",
  "is_del" varchar(1) COLLATE "pg_catalog"."default",
  "create_time" varchar(50) COLLATE "pg_catalog"."default",
  "message" varchar(50) COLLATE "pg_catalog"."default"
);
COMMENT ON COLUMN "public"."user_account"."id" IS '记录ID';
COMMENT ON COLUMN "public"."user_account"."user_id" IS '用户ID';
COMMENT ON COLUMN "public"."user_account"."oss_id" IS '平台编号ID';
COMMENT ON COLUMN "public"."user_account"."config_json" IS '发送方账号主体配置信息 相关项结构化数据';
COMMENT ON COLUMN "public"."user_account"."channel_code" IS '推送 的 厂商信息表 中的代码';
COMMENT ON COLUMN "public"."user_account"."is_del" IS '是否删除';
COMMENT ON COLUMN "public"."user_account"."create_time" IS '创建日期';
COMMENT ON COLUMN "public"."user_account"."message" IS '备注';
COMMENT ON TABLE "public"."user_account" IS '用户账号信息表 用于 用户 哪一个渠道的 推送渠道、账户的相关信息 核心为结构化数据';
-- ----------------------------
-- Table structure for warning_channel_classify
-- ----------------------------
DROP TABLE IF EXISTS "public"."warning_channel_classify";
CREATE TABLE "public"."warning_channel_classify" (
  "id" int4 NOT NULL DEFAULT nextval('warning_channel_classify_id_seq'::regclass),
  "type_name" varchar(255) COLLATE "pg_catalog"."default",
  "type_code" int4
);
COMMENT ON COLUMN "public"."warning_channel_classify"."id" IS '主键id';
COMMENT ON COLUMN "public"."warning_channel_classify"."type_name" IS '类型名称';
COMMENT ON COLUMN "public"."warning_channel_classify"."type_code" IS '类型代码 1--data预警报警，2--AI预警报警';
COMMENT ON TABLE "public"."warning_channel_classify" IS '报警渠道分类';
-- ----------------------------
-- Table structure for warning_rule_issue
-- ----------------------------
DROP TABLE IF EXISTS "public"."warning_rule_issue";
CREATE TABLE "public"."warning_rule_issue" (
  "id" int4 NOT NULL,
  "indictor_id" int4,
  "point_id" varchar(500) COLLATE "pg_catalog"."default",
  "warning_rule_id" int4,
  "start_time" varchar COLLATE "pg_catalog"."default",
  "end_time" varchar COLLATE "pg_catalog"."default" DEFAULT nextval('waring_rule_issue_id_seq'::regclass)
);
COMMENT ON COLUMN "public"."warning_rule_issue"."id" IS '主键';
COMMENT ON COLUMN "public"."warning_rule_issue"."indictor_id" IS '指标id';
COMMENT ON COLUMN "public"."warning_rule_issue"."point_id" IS '点位id';
COMMENT ON COLUMN "public"."warning_rule_issue"."warning_rule_id" IS '规则id';
COMMENT ON COLUMN "public"."warning_rule_issue"."start_time" IS '开始时间';
COMMENT ON COLUMN "public"."warning_rule_issue"."end_time" IS '结束时间';
-- ----------------------------
-- Table structure for warning_time_rule
-- ----------------------------
DROP TABLE IF EXISTS "public"."warning_time_rule";
CREATE TABLE "public"."warning_time_rule" (
  "id" int4 NOT NULL DEFAULT nextval('warning_time_rule_id_seq'::regclass),
  "time_rule" int4,
  "condition" varchar(255) COLLATE "pg_catalog"."default",
  "condition_value" int8,
  "indicator_id" int4,
  "unit" varchar(255) COLLATE "pg_catalog"."default",
  "unit_value" int8
);
COMMENT ON COLUMN "public"."warning_time_rule"."time_rule" IS '规则';
COMMENT ON COLUMN "public"."warning_time_rule"."condition" IS '条件';
COMMENT ON COLUMN "public"."warning_time_rule"."condition_value" IS '数值  单位ms(毫秒)';
COMMENT ON COLUMN "public"."warning_time_rule"."indicator_id" IS '指标id';
COMMENT ON COLUMN "public"."warning_time_rule"."unit" IS '单位';
COMMENT ON COLUMN "public"."warning_time_rule"."unit_value" IS '单位值';
-- ----------------------------
-- Primary Key structure for table ai_model_system
-- ----------------------------
ALTER TABLE "public"."ai_model_system"
ADD CONSTRAINT "ai_model_system_pkey" PRIMARY KEY ("id");
-- ----------------------------
-- Primary Key structure for table bdata_alert_push_mid
-- ----------------------------
ALTER TABLE "public"."bdata_alert_push_mid"
ADD CONSTRAINT "bdata_alert_push_mid_pkey" PRIMARY KEY ("id");
-- ----------------------------
-- Primary Key structure for table bdata_rule_push_mid
-- ----------------------------
ALTER TABLE "public"."bdata_rule_push_mid"
ADD CONSTRAINT "bdata_rule_push_mid_pkey" PRIMARY KEY ("id");
-- ----------------------------
-- Indexes structure for table gis_warning_alarm_info
-- ----------------------------
CREATE UNIQUE INDEX "pk_warning_alarm_info" ON "public"."gis_warning_alarm_info" USING btree (
  "warning_alarm_info_id" "pg_catalog"."int4_ops" ASC NULLS LAST
);
-- ----------------------------
-- Primary Key structure for table hazard_handle_employee
-- ----------------------------
ALTER TABLE "public"."hazard_handle_employee"
ADD CONSTRAINT "hazard_handle_employee_pkey" PRIMARY KEY ("id");
-- ----------------------------
-- Primary Key structure for table hazard_warning_alarm_handle
-- ----------------------------
ALTER TABLE "public"."hazard_warning_alarm_handle"
ADD CONSTRAINT "hazard_warning_alarm_handle_pkey" PRIMARY KEY ("id");
-- ----------------------------
-- Primary Key structure for table hazard_warning_time_rule
-- ----------------------------
ALTER TABLE "public"."hazard_warning_time_rule"
ADD CONSTRAINT "hazard_warning_time_rule_pkey" PRIMARY KEY ("id");
-- ----------------------------
-- Primary Key structure for table indicator_system_relative
-- ----------------------------
ALTER TABLE "public"."indicator_system_relative"
ADD CONSTRAINT "indicator_system_relative_pkey" PRIMARY KEY ("id");
-- ----------------------------
-- Primary Key structure for table push_sms
-- ----------------------------
ALTER TABLE "public"."push_sms"
ADD CONSTRAINT "push_sms_pkey" PRIMARY KEY ("id");
-- ----------------------------
-- Primary Key structure for table send_account
-- ----------------------------
ALTER TABLE "public"."send_account"
ADD CONSTRAINT "send_account_pkey" PRIMARY KEY ("id");
-- ----------------------------
-- Primary Key structure for table system_duration
-- ----------------------------
ALTER TABLE "public"."system_duration"
ADD CONSTRAINT "system_duration_pkey" PRIMARY KEY ("id");
-- ----------------------------
-- Primary Key structure for table warning_channel_classify
-- ----------------------------
ALTER TABLE "public"."warning_channel_classify"
ADD CONSTRAINT "warning_channel_classify_pkey" PRIMARY KEY ("id");
-- ----------------------------
-- Primary Key structure for table warning_rule_issue
-- ----------------------------
ALTER TABLE "public"."warning_rule_issue"
ADD CONSTRAINT "waring_rule_issue_pkey" PRIMARY KEY ("id");
-- ----------------------------
-- Primary Key structure for table warning_time_rule
-- ----------------------------
ALTER TABLE "public"."warning_time_rule"
ADD CONSTRAINT "warning_time_rule_pkey" PRIMARY KEY ("id");
