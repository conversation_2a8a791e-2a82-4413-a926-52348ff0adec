# =============================================================================
# YAML 模板定义 (按功能分组)
# =============================================================================

# 工具服务基础模板
x-tool-defaults: &tool_defaults
  restart: unless-stopped
  networks:
    - middle
  logging:
    driver: "json-file"
    options:
      max-size: "100m"
      max-file: "3"

# =============================================================================
# 网络和存储卷定义
# =============================================================================

networks:
  # 工具服务网络 - 与其他服务共享网络环境
  middle:
    external: true
    name: ${DOCKER_NETWORK}

volumes:
  # Prometheus 监控数据存储
  tool-prometheus-data:
    external: true
  # Grafana 仪表板数据存储
  tool-grafana-data:
    external: true
  # Portainer 容器管理数据存储
  tool-portainer-data:
    external: true

# =============================================================================
# 服务定义 (按监控架构和依赖关系排序)
# =============================================================================

services:
  # ---------------------------------------------------------------------------
  # 1. 数据收集层 - 监控数据采集服务
  # ---------------------------------------------------------------------------

  # Prometheus 监控数据收集和存储
  prometheus:
    <<: *tool_defaults
    image: harbor2.qdbdtd.com:8088/middleware/prometheus/prometheus:1.0
    container_name: prometheus
    hostname: prometheus
    user: root
    ports:
      - "39090:9090"
    volumes:
      # 监控数据持久化存储
      - tool-prometheus-data:/prometheus
      # 配置文件挂载
      - ./conf/prometheus.yml:/etc/prometheus/prometheus.yml
      - ./sd_config:/etc/prometheus/sd_config
      - ./rules:/etc/prometheus/rules
      # 时间同步
      - /etc/localtime:/etc/localtime:ro
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:9090/-/healthy"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # Node Exporter 主机监控数据采集
  node-exporter:
    <<: *tool_defaults
    image: harbor2.qdbdtd.com:8088/middleware/prometheus/node-exporter:1.0
    container_name: node-exporter
    hostname: node-exporter
    pid: host
    command:
      - '--path.rootfs=/host'
    volumes:
      # 主机文件系统挂载 (只读)
      - /:/host:ro,rslave

  # cAdvisor 容器监控数据采集
  cadvisor:
    <<: *tool_defaults
    image: harbor2.qdbdtd.com:8088/middleware/prometheus/cadvisor:1.0
    container_name: cadvisor
    hostname: cadvisor
    ports:
      - "38080:8080"
    volumes:
      # 容器运行时数据挂载
      - /:/rootfs:ro
      - /var/run:/var/run:rw
      - /sys:/sys:ro
      - /var/lib/docker/:/var/lib/docker:ro

  # Blackbox Exporter 网络探测监控
  blackbox-exporter:
    <<: *tool_defaults
    image: harbor2.qdbdtd.com:8088/middleware/prometheus/blackbox-exporter:1.0
    container_name: blackbox-exporter
    hostname: blackbox-exporter
    ports:
      - "39115:9115"

  # ---------------------------------------------------------------------------
  # 2. 告警处理层 - 告警管理和通知服务
  # ---------------------------------------------------------------------------

  # AlertManager 告警管理和路由
  alertmanager:
    <<: *tool_defaults
    image: harbor2.qdbdtd.com:8088/middleware/prometheus/alertmanager:1.0
    container_name: alertmanager
    hostname: alertmanager
    ports:
      - "39093:9093"
    volumes:
      # 告警配置文件
      - ./conf/alertmanager.yml:/etc/alertmanager/alertmanager.yml
    depends_on:
      - prometheus
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:9093/-/healthy"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # 钉钉告警服务 (自定义告警推送)
  alone-alert:
    <<: *tool_defaults
    image: harbor2.qdbdtd.com:8088/middleware/prometheus/alone-alert:1.0
    container_name: alone-alert
    hostname: alone-alert
    environment:
      # 钉钉告警配置
      - TOKEN=${DINGDING_TOKEN}
      - OPTS=${DINGDING_OPTS}
    ports:
      - "35000:5000"
    depends_on:
      - alertmanager

  # ---------------------------------------------------------------------------
  # 3. 数据可视化层 - 监控仪表板和界面
  # ---------------------------------------------------------------------------

  # Grafana 监控数据可视化仪表板
  grafana:
    <<: *tool_defaults
    image: harbor2.qdbdtd.com:8088/middleware/prometheus/grafana:1.0
    container_name: grafana
    hostname: grafana
    ports:
      - "30000:3000"
    environment:
      # Grafana 管理员密码配置
      GF_SECURITY_ADMIN_PASSWORD__FILE: /tmp/grafana_password
    volumes:
      # 仪表板数据持久化存储
      - tool-grafana-data:/var/lib/grafana
      # 配置文件和密码文件
      - ./conf/grafana.ini:/etc/grafana/grafana.ini
      - ./secret/grafana_password:/tmp/grafana_password
      # 时间同步
      - /etc/localtime:/etc/localtime:ro
    depends_on:
      - prometheus
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:3000/api/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # ---------------------------------------------------------------------------
  # 4. 容器管理层 - Docker 容器管理界面
  # ---------------------------------------------------------------------------

  # Portainer 容器管理界面
  portainer:
    <<: *tool_defaults
    image: harbor2.qdbdtd.com:8088/middleware/prometheus/portainer:1.0
    container_name: portainer
    command:
      - -H
      - unix:///var/run/docker.sock
      - --admin-password-file
      - /tmp/portainer_password
    volumes:
      # Docker Socket 挂载
      - /var/run/docker.sock:/var/run/docker.sock
      # Portainer 数据持久化存储
      - tool-portainer-data:/data
      # 管理员密码文件
      - ./secret/portainer_password:/tmp/portainer_password
    ports:
      - "39000:9000"
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:9000/api/status"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

