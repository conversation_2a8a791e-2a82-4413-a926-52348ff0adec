# =============================================================================
# SSH 调试服务 Docker Compose 配置文件
# =============================================================================

# =============================================================================
# 网络配置
# =============================================================================

networks:
  # 中台统一网络 - 与其他服务共享网络环境
  middle:
    external: true
    name: ${DOCKER_NETWORK}

# =============================================================================
# 服务定义
# =============================================================================

services:
  # ---------------------------------------------------------------------------
  # SSH 远程访问服务
  # ---------------------------------------------------------------------------

  ssh:
    container_name: ssh-debugger
    # 服务配置 - 默认不自动重启，需要手动启动
    restart: "no"
    #==============================================================
    #image: harbor2.qdbdtd.com:8088/middleware/alpine-ssh:3.12
    ## 服务配置 - 默认不自动重启，需要手动启动
    #restart: "no"
    ## 环境变量配置
    #environment:
    #  # SSH 登录密码 (从环境变量读取)
    #  - PASSWORD=${SSH_PASSWORD}
    ## 端口映射配置
    #ports:
    #  # 将容器的 22 端口映射到宿主机指定端口
    #  - "${SSH_PORT}:22"
    #--------------------------------------------------------------
    # 使用 Alpine Linux 基础的轻量级 SSH 服务镜像 linuxserver/openssh-server
    image: harbor2.qdbdtd.com:8088/middleware/openssh-server:amd64-20250728
    # 环境变量配置
    environment:
      # 从环境变量读取
      - PUID=1000
      - PGID=1000
      - TZ=Aisa/Shanghai
      - PASSWORD_ACCESS=true
      - SUDO_ACCESS=false
      - USER_NAME=${SSH_USERNAME}
      - USER_PASSWORD=${SSH_PASSWORD}
    volumes:
      - "./config:/config"
    # 端口映射配置
    ports:
      # 将容器 SSH Server 默认的 2222 端口映射到宿主机指定端口
      - "${SSH_PORT}:2222"
    #==============================================================
    # 网络配置 - 接入中台统一网络
    networks:
      - middle
    # 日志配置
    logging:
      driver: "json-file"
      options:
        max-size: "50m"
        max-file: "3"
