DROP TYPE IF EXISTS "public"."video_device_type_enum";
CREATE TYPE "public"."video_device_type_enum" AS ENUM (
  'hikvision',
  'dahua',
  'hxtx',
  'uniview'
);
CREATE TABLE public.video_channel_group (
  id serial4 NOT NULL, -- 自增ID
  name varchar NOT NULL, -- 视频通道组名称
  CONSTRAINT video_channel_group_name_key UNIQUE (name),
  CONSTRAINT video_channel_group_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.video_channel_group IS '视频通道组';
-- <PERSON><PERSON><PERSON> comments
COMMENT ON COLUMN public.video_channel_group.id IS '自增ID';
COMMENT ON COLUMN public.video_channel_group.name IS '视频通道组名称';
-- public.video_device definition
-- Drop table
-- DROP TABLE public.video_device;
CREATE TABLE public.video_device (
  id serial4 NOT NULL,
  ip varchar(80) NOT NULL,
  port int4 NOT NULL,
  "user" varchar(80) NOT NULL,
  "password" varchar(80) NOT NULL,
  "type" varchar(80) NOT NULL,
  device_name varchar(80) NULL,
  device_type varchar(80) NULL,
  state varchar(80) NULL,
  protocol_name varchar(80) NULL,
  protocol_port int4 NULL,
  model varchar(80) NULL,
  device_ip varchar(80) NULL,
  device_port varchar(80) NULL,
  CONSTRAINT video_device_pkey PRIMARY KEY (id)
);
-- public.video_channel definition
-- Drop table
-- DROP TABLE public.video_channel;
CREATE TABLE public.video_channel (
  id serial4 NOT NULL,
  device_id int4 NOT NULL,
  name varchar(80) NOT NULL,
  input_port int4 NOT NULL,
  online bool NOT NULL,
  play_url varchar(80) NULL,
  vcodec varchar(80) NULL,
  acodec varchar(80) NULL,
  video_enable bool NULL,
  audio_enable bool NULL,
  stream_type int4 DEFAULT 2,
  mining_area_id int4 NULL, -- 采区ID
  geolocation_area_id int4 NULL, -- 区域ID
  CONSTRAINT video_channel_pkey PRIMARY KEY (id)
);
-- Column comments
COMMENT ON COLUMN public.video_channel.mining_area_id IS '采区ID';
COMMENT ON COLUMN public.video_channel.geolocation_area_id IS '区域ID';
-- public.video_channel_map definition
-- Drop table
-- DROP TABLE public.video_channel_map;
CREATE TABLE public.video_channel_map (
  id serial4 NOT NULL, -- 自增ID
  video_channel_group_id int4 NOT NULL, -- 视频通道组ID
  video_channel_id int4 NOT NULL, -- 视频通道ID
  CONSTRAINT group_id_channel UNIQUE (video_channel_group_id, video_channel_id),
  CONSTRAINT video_channel_map_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.video_channel_map IS '视频通道映射表';
-- Column comments
COMMENT ON COLUMN public.video_channel_map.id IS '自增ID';
COMMENT ON COLUMN public.video_channel_map.video_channel_group_id IS '视频通道组ID';
COMMENT ON COLUMN public.video_channel_map.video_channel_id IS '视频通道ID';
-- public.video_livewall_alert definition
-- Drop table
-- DROP TABLE public.video_livewall_alert;
CREATE TABLE public.video_livewall_alert (
  id serial4 NOT NULL,
  video_channel_id int4 NOT NULL,
  alert_name text NOT NULL,
  CONSTRAINT video_livewall_alert_pkey PRIMARY KEY (id),
  CONSTRAINT video_livewall_alert_video_channel_id_key UNIQUE (video_channel_id)
);
COMMENT ON TABLE public.video_livewall_alert IS 'LiveWall视频实时报警';
-- public.video_channel foreign keys
ALTER TABLE public.video_channel
ADD CONSTRAINT video_channel_device_id_fkey FOREIGN KEY (device_id) REFERENCES public.video_device(id) ON DELETE CASCADE;
-- ALTER TABLE public.video_channel ADD CONSTRAINT video_channel_geolocation_area_id_fkey FOREIGN KEY (geolocation_area_id) REFERENCES public.geolocation_area(id) ON DELETE SET NULL ON UPDATE CASCADE;
-- ALTER TABLE public.video_channel ADD CONSTRAINT video_channel_mining_area_id_fkey FOREIGN KEY (mining_area_id) REFERENCES public.mining_area(id) ON DELETE SET NULL ON UPDATE CASCADE;
-- public.video_channel_map foreign keys
ALTER TABLE public.video_channel_map
ADD CONSTRAINT video_channel_map_video_channel_group_id_fkey FOREIGN KEY (video_channel_group_id) REFERENCES public.video_channel_group(id) ON DELETE CASCADE ON UPDATE CASCADE;
ALTER TABLE public.video_channel_map
ADD CONSTRAINT video_channel_map_video_channel_id_fkey FOREIGN KEY (video_channel_id) REFERENCES public.video_channel(id) ON DELETE CASCADE ON UPDATE CASCADE;
-- public.video_livewall_alert foreign keys
ALTER TABLE public.video_livewall_alert
ADD CONSTRAINT video_livewall_alert_video_channel_id_fkey FOREIGN KEY (video_channel_id) REFERENCES public.video_channel(id);
-- public.viewport definition
-- Drop table
-- DROP TABLE public.viewport;
CREATE TABLE public.viewport (
  id serial4 NOT NULL, -- 自增ID
  "name" varchar NOT NULL, -- 视口名称
  meta jsonb NOT NULL, -- 视口源信息
  "snapshot" jsonb NULL, -- 快照信息
  CONSTRAINT viewport_pkey PRIMARY KEY (id)
);
CREATE UNIQUE INDEX ix_viewport_name ON public.viewport USING btree (name);
COMMENT ON TABLE public.viewport IS '三维视口';
-- Column comments
COMMENT ON COLUMN public.viewport.id IS '自增ID';
COMMENT ON COLUMN public.viewport."name" IS '视口名称';
COMMENT ON COLUMN public.viewport.meta IS '视口源信息';
COMMENT ON COLUMN public.viewport."snapshot" IS '快照信息';
-- 不知道啥作用
-- public.config_map definition
-- Drop table
-- DROP TABLE public.config_map;
CREATE TABLE public.config_map (
  guid text NOT NULL,
  "name" text NOT NULL,
  CONSTRAINT config_map_pkey PRIMARY KEY (guid)
);
COMMENT ON TABLE public.config_map IS '配置一张图';
-- public.config_map_category definition
-- Drop table
-- DROP TABLE public.config_map_category;
CREATE TABLE public.config_map_category (
  id serial4 NOT NULL,
  "name" text NOT NULL,
  code text NOT NULL,
  map_guid text NULL,
  CONSTRAINT config_map_category_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.config_map_category IS '一张图配置类型';
-- public.config_map_category foreign keys
ALTER TABLE public.config_map_category
ADD CONSTRAINT config_map_category_map_guid_fkey FOREIGN KEY (map_guid) REFERENCES public.config_map(guid) ON DELETE
SET NULL ON UPDATE CASCADE;
