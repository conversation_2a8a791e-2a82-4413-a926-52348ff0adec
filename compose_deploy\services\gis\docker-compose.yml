#===============================================================================
# YAML 锚点定义 - 可复用的配置模板
#===============================================================================

# GIS 服务通用配置
x-gis-defaults: &gis_defaults
  restart: unless-stopped
  logging:
    driver: "json-file"
    options:
      max-size: "1g"
      max-file: "2"
  networks:
    - middle

# Nginx 前端服务配置模板
x-nginx-defaults: &nginx_defaults
  <<: *gis_defaults
  env_file:
    - ../base/.env
  healthcheck:
    test: ["CMD", "nginx", "-t"]
    interval: 30s
    timeout: 10s
    retries: 3
    start_period: 40s

# GIS 前端服务配置模板
x-gis-frontend-defaults: &gis_frontend_defaults
  <<: *nginx_defaults
  command: nginx -g 'daemon off;'
  volumes:
    - "../mos/config/nginx-default.conf:/etc/nginx/conf.d/default.conf:ro"

# GIS 后端服务配置模板
x-gis-backend-defaults: &gis_backend_defaults
  <<: *gis_defaults
  env_file:
    - ../base/.env
  healthcheck:
    test: ["CMD", "curl", "-f", "http://localhost:9001/health"]
    interval: 30s
    timeout: 10s
    retries: 3
    start_period: 60s

#===============================================================================
# 网络配置
#===============================================================================

networks:
  middle:
    external: true
    name: ${DOCKER_NETWORK}

#===============================================================================
# 数据卷配置
#===============================================================================

volumes:
  gis-biz-log:
    external: true

#===============================================================================
# 服务定义 - 按业务依赖关系排序
#===============================================================================

services:
  #-----------------------------------------------------------------------------
  # 后端服务 - 业务逻辑层（优先启动）
  #-----------------------------------------------------------------------------

  # GIS 业务系统后端服务
  backend-gis-biz:
    <<: *gis_backend_defaults
    container_name: backend-gis-biz
    image: harbor2.qdbdtd.com:8088/bjproduct/platform/services/gis_biz:${BACKEND_GIS_VERSION}
    environment:
      # 核心业务配置
      - FACE_AUTO_ADVANCE=${FACE_AUTO_ADVANCE}
      - BLADE_MINIO_HOST=${BLADE_MINIO_HOST}
      - PORT_RABBITMQ_DASHBOARD=${PORT_RABBITMQ_DASHBOARD}
      # 数据服务配置
      - CONVER_MONITOR_URL=${BACKEND_DATA_MONITOR}
      # 地图服务配置
      - SAFE_URL_HOST=${UPSTREAM_GIS_API}
      - luoyang_url=${luoyang_url}
      # 三维可视化配置
      - THREE_GIS_URL=${THREE_GIS_URL}
      - THREE_PASSWORD=${THREE_PASSWORD}
      - THREE_NAME=${THREE_NAME}
      - THREE_DEPLOY=${THREE_DEPLOY}
      ## 可选配置 (当前未启用)
      #- AI_LIVE_WALL_HOST=${AI_LIVE_WALL_HOST}
      #- AI_LIVE_WALL_PORT=${AI_LIVE_WALL_PORT}
      #- GAO_DE_URL=restapi.amap.com
      #- GAO_DE_KEY=${GAO_DE_KEY}
      #- SRS_SERVER_URL=${SRS_SERVER_URL}
      #- GIS_MINIO_BUCKETNAME=${GIS_MINIO_BUCKETNAME}
      #- GIS_MQ_EX=${GIS_MQ_EX}
      #- GIS_MQ_QUEUE=${GIS_MQ_QUEUE}
      #- GIS_MQ_DIRECT=${GIS_MQ_DIRECT}
    volumes:
      - "gis-biz-log:/home/<USER>/logs"
    #ports:
    #  - "${PORT_GIS_BIZ}:9001"

  #-----------------------------------------------------------------------------
  # 前端服务 - 用户界面层（依赖后端服务）
  #-----------------------------------------------------------------------------

  # GIS 前端服务
  frontend-module-gis:
    <<: *gis_frontend_defaults
    container_name: frontend-module-gis
    image: harbor2.qdbdtd.com:8088/bjproduct/platform/front-services/base-gis:${FRONTEND_GIS_VERSION}
    depends_on:
      - backend-gis-biz
    #ports:
    #  - "${PORT_GIS_FRONTEND}:80"
