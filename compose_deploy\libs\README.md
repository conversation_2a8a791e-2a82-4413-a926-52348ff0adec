# 核心库脚本目录

本目录包含 `compose_deploy.sh` 依赖的核心库脚本，提供部署、管理、备份等核心功能。

## 📁 目录内容

### 🛠️ utils.sh

**工具函数库**

**功能**：
- 路径变量定义
- Docker 网络和数据卷配置
- 颜色输出函数
- 系统要求检查
- 常用工具函数

### 🚀 service.sh

**服务部署管理工具**

**功能**：
- 各微服务的启动、停止、重启函数
- 服务健康检查
- 部署状态管理
- 服务依赖管理
- Hasura GraphQL 引擎状态检查

### 🐳 docker.sh

**Docker 核心管理工具**

**功能**：
- Docker 网络创建和删除
- Docker 数据卷管理和备份
- Docker Compose 操作封装
- 镜像拉取重试机制
- Harbor 登录认证

### 💾 database.sh

**数据库备份脚本**

**功能**：
- PostgreSQL 数据库备份
- MES 业务数据库备份
- 自动压缩和时间戳命名
- 备份文件管理

### 🔄 git.sh

**Git 版本管理**

**功能**：
- Git 版本切换
- 本地修改暂存和恢复
- 版本标签管理
- 自动合并处理

### 🗄️ psql-executor.sh

**PostgreSQL 执行器**

**功能**：
- PostgreSQL 连接检查
- SQL 脚本执行
- TDengine 时序数据库管理
- 数据库版本管理

### 🔄 migration.sh

**Hasura GraphQL 数据库迁移工具**

**功能**：
- 启动 Hasura 控制台进行数据库迁移管理
- 提供 GraphQL API 和管理界面
- 支持数据库架构变更和数据迁移
- 控制台端口: 9999，API 端口: 10000

### 📈 upgrade/

**版本升级脚本目录**

**功能**：
- 数据库结构升级
- 数据迁移脚本
- 版本兼容性处理

## 🔗 依赖关系

这些脚本被 `compose_deploy.sh` 通过以下方式加载：

```bash
# Include all subscripts
for scripts in $DEPLOY_PATH/libs/*.sh; do source "$scripts"; done
```

## 📋 主要函数

### 部署相关

- `up_*_service()` - 上线各种服务 (docker compose up -d)
- `down_*_service()` - 下线各种服务 (docker compose down)
- `start_*_service()` - 启动各种服务 (docker compose start)
- `stop_*_service()` - 停止各种服务 (docker compose stop)
- `restart_*_service()` - 重启各种服务 (docker compose restart)
- `check_hasura_up()` - 检查 Hasura GraphQL 引擎状态

### 迁移相关

- `start_hasura_console()` - 启动 Hasura 控制台进行数据库迁移管理

### Git 相关

- `git_update()` - 更新部署材料到指定的 Git 标签版本

### Docker 相关

- `create_docker_network()` - 创建 Docker 网络
- `create_docker_volume()` - 创建 Docker 数据卷
- `remove_docker_network()` - 删除 Docker 网络
- `remove_docker_volume()` - 删除 Docker 数据卷
- `remove_docker_volumes_safely()` - 安全删除指定的 Docker 数据卷（支持通配符）
- `backup_docker_volume()` - 备份 Docker 数据卷
- `restore_docker_volume()` - 恢复 Docker 数据卷
- `docker_compose_up()` - 启动 Docker Compose 服务
- `docker_compose_down()` - 停止并删除 Docker Compose 服务
- `docker_compose_start()` - 启动 Docker Compose 服务
- `docker_compose_stop()` - 停止 Docker Compose 服务
- `docker_compose_restart()` - 重启 Docker Compose 服务
- `docker_compose_pull_with_retry()` - 带重试的镜像拉取
- `init_docker()` - 初始化 Docker 环境
- `prune_docker()` - 清理 Docker 资源

### 数据库相关

- `backup_postgres()` - 备份 PostgreSQL 数据库
- `restore_postgres()` - 恢复 PostgreSQL 数据库
- `list_backups()` - 列出可用备份文件
- `cleanup_old_backups()` - 清理过期备份

### PostgreSQL 执行器相关

- `psqlCheckConn()` - 检查 PostgreSQL 数据库连接
- `psqlCheckSchema()` - 检查数据库 schema 是否存在
- `psqlGetCurVer()` - 获取当前数据库版本
- `psqlCreateCvsTable()` - 创建数据库版本管理表
- `psqlMarkVersion()` - 标记数据库版本
- `psqlExecSqlFile()` - 执行 SQL 文件
- `psqlExecSql()` - 执行 SQL 语句
- `psqlGetSchema()` - 从连接字符串提取 schema 名称

### TDengine 相关

- `tdsqlCheckConn()` - 检查 TDengine 数据库连接
- `tdsqlImport()` - 导入 TDengine 初始化脚本
- `tdsqlCheckInit()` - 检查 TDengine 初始化状态

### 工具函数

- `echo_yellow()` - 黄色输出
- `echo_error()` - 错误输出
- `echo_ok()` - 成功输出
- `ver()` - 版本号转换为可比较数字
- `compare_version()` - 比较两个版本号
- `index_of()` - 在数组中查找元素索引
- `check_system()` - 检查系统要求
- `check_config_env()` - 检查环境配置

## ⚠️ 注意事项

- 这些脚本不应该直接执行，而是被 `compose_deploy.sh` 调用
- 修改这些脚本可能影响整个部署系统
- 所有函数都依赖于 `utils.sh` 中定义的变量和函数
