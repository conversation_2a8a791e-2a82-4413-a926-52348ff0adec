-- public.blade_attach definition

-- Drop table

-- DROP TABLE public.blade_attach;

CREATE TABLE public.blade_attach (
	id int8 NOT NULL, -- 主键
	tenant_id varchar(12) NULL, -- 租户ID
	link varchar(1000) NULL, -- 附件地址
	"domain" varchar(500) NULL, -- 附件域名
	name varchar(500) NULL, -- 附件名称
	original_name varchar(500) NULL, -- 附件原名
	"extension" varchar(12) NULL, -- 附件拓展名
	attach_size int8 NULL, -- 附件大小
	create_user int8 NULL, -- 创建人
	create_dept int8 NULL, -- 创建部门
	create_time timestamp(6) NULL, -- 创建时间
	update_user int8 NULL, -- 修改人
	update_time timestamp(6) NULL, -- 修改时间
	status int4 NULL, -- 状态
	is_deleted int4 NULL, -- 是否已删除
	CONSTRAINT blade_attach_pkey PRIMARY KEY (id)
);

-- <PERSON><PERSON><PERSON> comments

COMMENT ON COLUMN public.blade_attach.id IS '主键';
COMMENT ON COLUMN public.blade_attach.tenant_id IS '租户ID';
COMMENT ON COLUMN public.blade_attach.link IS '附件地址';
COMMENT ON COLUMN public.blade_attach."domain" IS '附件域名';
COMMENT ON COLUMN public.blade_attach.name IS '附件名称';
COMMENT ON COLUMN public.blade_attach.original_name IS '附件原名';
COMMENT ON COLUMN public.blade_attach."extension" IS '附件拓展名';
COMMENT ON COLUMN public.blade_attach.attach_size IS '附件大小';
COMMENT ON COLUMN public.blade_attach.create_user IS '创建人';
COMMENT ON COLUMN public.blade_attach.create_dept IS '创建部门';
COMMENT ON COLUMN public.blade_attach.create_time IS '创建时间';
COMMENT ON COLUMN public.blade_attach.update_user IS '修改人';
COMMENT ON COLUMN public.blade_attach.update_time IS '修改时间';
COMMENT ON COLUMN public.blade_attach.status IS '状态';
COMMENT ON COLUMN public.blade_attach.is_deleted IS '是否已删除';


-- public.blade_card_edit definition

-- Drop table

-- DROP TABLE public.blade_card_edit;

CREATE TABLE public.blade_card_edit (
	id serial4 NOT NULL,
	name varchar(255) NOT NULL, -- 名称
	description varchar(255) NULL, -- 描述
	info_data varchar(5000) NULL, -- 大Json
	create_time timestamp(6) NULL, -- 创建时间
	update_time timestamp(6) NULL, -- 更新时间
	edition int4 NULL DEFAULT 1, -- 版本
	CONSTRAINT mos_card_info_pkey PRIMARY KEY (id)
);

-- Column comments

COMMENT ON COLUMN public.blade_card_edit.name IS '名称';
COMMENT ON COLUMN public.blade_card_edit.description IS '描述';
COMMENT ON COLUMN public.blade_card_edit.info_data IS '大Json';
COMMENT ON COLUMN public.blade_card_edit.create_time IS '创建时间';
COMMENT ON COLUMN public.blade_card_edit.update_time IS '更新时间';
COMMENT ON COLUMN public.blade_card_edit.edition IS '版本';


-- public.blade_card_edit_history definition

-- Drop table

-- DROP TABLE public.blade_card_edit_history;

CREATE TABLE public.blade_card_edit_history (
	id serial4 NOT NULL,
	name varchar(255) NULL, -- 名称
	description varchar(255) NULL, -- 描述
	info_data varchar(5000) NULL, -- 大Json
	create_time timestamp(6) NULL, -- 创建时间
	update_time timestamp(6) NULL, -- 更新时间
	edition int4 NULL, -- 版本
	info_id int4 NOT NULL, -- 实时表的主键
	CONSTRAINT mos_configure_info_copy1_pkey PRIMARY KEY (id)
);

-- Column comments

COMMENT ON COLUMN public.blade_card_edit_history.name IS '名称';
COMMENT ON COLUMN public.blade_card_edit_history.description IS '描述';
COMMENT ON COLUMN public.blade_card_edit_history.info_data IS '大Json';
COMMENT ON COLUMN public.blade_card_edit_history.create_time IS '创建时间';
COMMENT ON COLUMN public.blade_card_edit_history.update_time IS '更新时间';
COMMENT ON COLUMN public.blade_card_edit_history.edition IS '版本';
COMMENT ON COLUMN public.blade_card_edit_history.info_id IS '实时表的主键';


-- public.blade_client definition

-- Drop table

-- DROP TABLE public.blade_client;

CREATE TABLE public.blade_client (
	id int8 NOT NULL, -- 主键
	client_id varchar(48) NOT NULL, -- 客户端id
	client_secret varchar(256) NOT NULL, -- 客户端密钥
	resource_ids varchar(256) NULL, -- 资源集合
	"scope" varchar(256) NOT NULL, -- 授权范围
	authorized_grant_types varchar(256) NOT NULL, -- 授权类型
	web_server_redirect_uri varchar(256) NULL, -- 回调地址
	authorities varchar(256) NULL, -- 权限
	access_token_validity int4 NOT NULL, -- 令牌过期秒数
	refresh_token_validity int4 NOT NULL, -- 刷新令牌过期秒数
	additional_information varchar(4096) NULL, -- 附加说明
	autoapprove varchar(256) NULL, -- 自动授权
	create_user int8 NULL, -- 创建人
	create_dept int8 NULL, -- 创建部门
	create_time timestamp(6) NULL, -- 创建时间
	update_user int8 NULL, -- 修改人
	update_time timestamp(6) NULL, -- 修改时间
	status int4 NOT NULL, -- 状态
	is_deleted int4 NOT NULL, -- 是否已删除
	CONSTRAINT blade_client_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.blade_client IS '客户端表';

-- Column comments

COMMENT ON COLUMN public.blade_client.id IS '主键';
COMMENT ON COLUMN public.blade_client.client_id IS '客户端id';
COMMENT ON COLUMN public.blade_client.client_secret IS '客户端密钥';
COMMENT ON COLUMN public.blade_client.resource_ids IS '资源集合';
COMMENT ON COLUMN public.blade_client."scope" IS '授权范围';
COMMENT ON COLUMN public.blade_client.authorized_grant_types IS '授权类型';
COMMENT ON COLUMN public.blade_client.web_server_redirect_uri IS '回调地址';
COMMENT ON COLUMN public.blade_client.authorities IS '权限';
COMMENT ON COLUMN public.blade_client.access_token_validity IS '令牌过期秒数';
COMMENT ON COLUMN public.blade_client.refresh_token_validity IS '刷新令牌过期秒数';
COMMENT ON COLUMN public.blade_client.additional_information IS '附加说明';
COMMENT ON COLUMN public.blade_client.autoapprove IS '自动授权';
COMMENT ON COLUMN public.blade_client.create_user IS '创建人';
COMMENT ON COLUMN public.blade_client.create_dept IS '创建部门';
COMMENT ON COLUMN public.blade_client.create_time IS '创建时间';
COMMENT ON COLUMN public.blade_client.update_user IS '修改人';
COMMENT ON COLUMN public.blade_client.update_time IS '修改时间';
COMMENT ON COLUMN public.blade_client.status IS '状态';
COMMENT ON COLUMN public.blade_client.is_deleted IS '是否已删除';


-- public.blade_code definition

-- Drop table

-- DROP TABLE public.blade_code;

CREATE TABLE public.blade_code (
	id int8 NOT NULL, -- 主键
	datasource_id int8 NULL, -- 数据源主键
	service_name varchar(64) NULL, -- 服务名称
	code_name varchar(64) NULL, -- 模块名称
	table_name varchar(64) NULL, -- 表名
	table_prefix varchar(64) NULL, -- 表前缀
	pk_name varchar(32) NULL, -- 主键名
	package_name varchar(500) NULL, -- 后端包名
	base_mode int2 NULL, -- 基础业务模式
	wrap_mode int2 NULL, -- 包装器模式
	api_path varchar(2000) NULL, -- 后端路径
	web_path varchar(2000) NULL, -- 前端路径
	is_deleted int4 NULL, -- 是否已删除
	CONSTRAINT blade_code_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.blade_code IS '代码生成表';

-- Column comments

COMMENT ON COLUMN public.blade_code.id IS '主键';
COMMENT ON COLUMN public.blade_code.datasource_id IS '数据源主键';
COMMENT ON COLUMN public.blade_code.service_name IS '服务名称';
COMMENT ON COLUMN public.blade_code.code_name IS '模块名称';
COMMENT ON COLUMN public.blade_code.table_name IS '表名';
COMMENT ON COLUMN public.blade_code.table_prefix IS '表前缀';
COMMENT ON COLUMN public.blade_code.pk_name IS '主键名';
COMMENT ON COLUMN public.blade_code.package_name IS '后端包名';
COMMENT ON COLUMN public.blade_code.base_mode IS '基础业务模式';
COMMENT ON COLUMN public.blade_code.wrap_mode IS '包装器模式';
COMMENT ON COLUMN public.blade_code.api_path IS '后端路径';
COMMENT ON COLUMN public.blade_code.web_path IS '前端路径';
COMMENT ON COLUMN public.blade_code.is_deleted IS '是否已删除';


-- public.blade_datasource definition

-- Drop table

-- DROP TABLE public.blade_datasource;

CREATE TABLE public.blade_datasource (
	id int8 NOT NULL, -- 主键
	name varchar(100) NULL, -- 名称
	driver_class varchar(100) NULL, -- 驱动类
	url varchar(500) NULL, -- 链接地址
	username varchar(45) NULL, -- 用户名
	"password" varchar(45) NULL, -- 密码
	remark varchar(500) NULL, -- 备注
	create_user int8 NULL, -- 创建人
	create_dept int8 NULL, -- 创建部门
	create_time timestamp(6) NULL, -- 创建时间
	update_user int8 NULL, -- 修改人
	update_time timestamp(6) NULL, -- 修改时间
	status int4 NULL, -- 状态
	is_deleted int4 NULL, -- 是否已删除
	CONSTRAINT blade_datasource_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.blade_datasource IS '数据源配置表';

-- Column comments

COMMENT ON COLUMN public.blade_datasource.id IS '主键';
COMMENT ON COLUMN public.blade_datasource.name IS '名称';
COMMENT ON COLUMN public.blade_datasource.driver_class IS '驱动类';
COMMENT ON COLUMN public.blade_datasource.url IS '链接地址';
COMMENT ON COLUMN public.blade_datasource.username IS '用户名';
COMMENT ON COLUMN public.blade_datasource."password" IS '密码';
COMMENT ON COLUMN public.blade_datasource.remark IS '备注';
COMMENT ON COLUMN public.blade_datasource.create_user IS '创建人';
COMMENT ON COLUMN public.blade_datasource.create_dept IS '创建部门';
COMMENT ON COLUMN public.blade_datasource.create_time IS '创建时间';
COMMENT ON COLUMN public.blade_datasource.update_user IS '修改人';
COMMENT ON COLUMN public.blade_datasource.update_time IS '修改时间';
COMMENT ON COLUMN public.blade_datasource.status IS '状态';
COMMENT ON COLUMN public.blade_datasource.is_deleted IS '是否已删除';


-- public.blade_dept definition

-- Drop table

-- DROP TABLE public.blade_dept;

CREATE TABLE public.blade_dept (
	id int8 NOT NULL, -- 主键
	tenant_id varchar(12) NULL, -- 租户ID
	parent_id int8 NULL, -- 父主键
	ancestors varchar(2000) NULL, -- 祖级列表
	dept_category int4 NULL, -- 部门类型
	dept_name varchar(45) NULL, -- 部门名
	full_name varchar(45) NULL, -- 部门全称
	sort int4 NULL, -- 排序
	remark varchar(255) NULL, -- 备注
	is_deleted int4 NULL, -- 是否已删除
	org_id int8 NULL, -- 机构ID
	leader_user_id int8 NULL, -- 领导用户id
	receive_code varchar(255) NULL DEFAULT NULL::character varying, -- 接受推送数据存编码时使用，其他情况无用
	CONSTRAINT blade_dept_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.blade_dept IS '机构表';

-- Column comments

COMMENT ON COLUMN public.blade_dept.id IS '主键';
COMMENT ON COLUMN public.blade_dept.tenant_id IS '租户ID';
COMMENT ON COLUMN public.blade_dept.parent_id IS '父主键';
COMMENT ON COLUMN public.blade_dept.ancestors IS '祖级列表';
COMMENT ON COLUMN public.blade_dept.dept_category IS '部门类型';
COMMENT ON COLUMN public.blade_dept.dept_name IS '部门名';
COMMENT ON COLUMN public.blade_dept.full_name IS '部门全称';
COMMENT ON COLUMN public.blade_dept.sort IS '排序';
COMMENT ON COLUMN public.blade_dept.remark IS '备注';
COMMENT ON COLUMN public.blade_dept.is_deleted IS '是否已删除';
COMMENT ON COLUMN public.blade_dept.org_id IS '机构ID';
COMMENT ON COLUMN public.blade_dept.leader_user_id IS '领导用户id';
COMMENT ON COLUMN public.blade_dept.receive_code IS '接受推送数据存编码时使用，其他情况无用';


-- public.blade_dict definition

-- Drop table

-- DROP TABLE public.blade_dict;

CREATE TABLE public.blade_dict (
	id int8 NOT NULL, -- 主键
	parent_id int8 NULL, -- 父主键
	code varchar(255) NULL, -- 字典码
	dict_key varchar(255) NULL, -- 字典值
	dict_value varchar(255) NULL, -- 字典名称
	sort int4 NULL, -- 排序
	remark varchar(255) NULL, -- 字典备注
	is_sealed int4 NULL, -- 是否已封存
	is_deleted int4 NULL, -- 是否已删除
	CONSTRAINT blade_dict_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.blade_dict IS '字典表';

-- Column comments

COMMENT ON COLUMN public.blade_dict.id IS '主键';
COMMENT ON COLUMN public.blade_dict.parent_id IS '父主键';
COMMENT ON COLUMN public.blade_dict.code IS '字典码';
COMMENT ON COLUMN public.blade_dict.dict_key IS '字典值';
COMMENT ON COLUMN public.blade_dict.dict_value IS '字典名称';
COMMENT ON COLUMN public.blade_dict.sort IS '排序';
COMMENT ON COLUMN public.blade_dict.remark IS '字典备注';
COMMENT ON COLUMN public.blade_dict.is_sealed IS '是否已封存';
COMMENT ON COLUMN public.blade_dict.is_deleted IS '是否已删除';


-- public.blade_dict_biz definition

-- Drop table

-- DROP TABLE public.blade_dict_biz;

CREATE TABLE public.blade_dict_biz (
	id int8 NOT NULL, -- 主键
	tenant_id varchar(12) NULL, -- 租户ID
	parent_id int8 NULL, -- 父主键
	code varchar(255) NULL, -- 字典码
	dict_key varchar(255) NULL, -- 字典值
	dict_value varchar(255) NULL, -- 字典名称
	sort int4 NULL, -- 排序
	remark varchar(255) NULL, -- 字典备注
	is_sealed int4 NULL, -- 是否已封存
	is_deleted int4 NULL, -- 是否已删除
	CONSTRAINT blade_dict_biz_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.blade_dict_biz IS '业务字典表';

-- Column comments

COMMENT ON COLUMN public.blade_dict_biz.id IS '主键';
COMMENT ON COLUMN public.blade_dict_biz.tenant_id IS '租户ID';
COMMENT ON COLUMN public.blade_dict_biz.parent_id IS '父主键';
COMMENT ON COLUMN public.blade_dict_biz.code IS '字典码';
COMMENT ON COLUMN public.blade_dict_biz.dict_key IS '字典值';
COMMENT ON COLUMN public.blade_dict_biz.dict_value IS '字典名称';
COMMENT ON COLUMN public.blade_dict_biz.sort IS '排序';
COMMENT ON COLUMN public.blade_dict_biz.remark IS '字典备注';
COMMENT ON COLUMN public.blade_dict_biz.is_sealed IS '是否已封存';
COMMENT ON COLUMN public.blade_dict_biz.is_deleted IS '是否已删除';

-- public.blade_group_user definition

-- Drop table

-- DROP TABLE public.blade_group_user;

CREATE TABLE public.blade_group_user (
	id int8 NOT NULL, -- 主键
	user_id int8 NULL, -- 用户id
	user_group_id int8 NULL, -- 用户组ID
	org_id int8 NULL, -- 机构ID
	CONSTRAINT blade_user_biz_bind_group_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.blade_group_user IS '用户-业务关联表';

-- Column comments

COMMENT ON COLUMN public.blade_group_user.id IS '主键';
COMMENT ON COLUMN public.blade_group_user.user_id IS '用户id';
COMMENT ON COLUMN public.blade_group_user.user_group_id IS '用户组ID';
COMMENT ON COLUMN public.blade_group_user.org_id IS '机构ID';


-- public.blade_log_api definition

-- Drop table

-- DROP TABLE public.blade_log_api;

CREATE TABLE public.blade_log_api (
	id int8 NOT NULL, -- 编号
	tenant_id varchar(12) NULL, -- 租户ID
	service_id varchar(32) NULL, -- 服务ID
	server_host varchar(255) NULL, -- 服务器名
	server_ip varchar(255) NULL, -- 服务器IP地址
	env varchar(255) NULL, -- 服务器环境
	"type" bpchar(1) NULL, -- 日志类型
	title varchar(255) NULL, -- 日志标题
	"method" varchar(10) NULL, -- 操作方式
	request_uri varchar(255) NULL, -- 请求URI
	user_agent varchar(1000) NULL, -- 用户代理
	remote_ip varchar(255) NULL, -- 操作IP地址
	method_class varchar(255) NULL, -- 方法类
	method_name varchar(255) NULL, -- 方法名
	params text NULL, -- 操作提交的数据
	"time" varchar(64) NULL, -- 执行时间
	create_by varchar(64) NULL, -- 创建者
	create_time timestamp(6) NULL, -- 创建时间
	CONSTRAINT blade_log_api_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.blade_log_api IS '接口日志表';

-- Column comments

COMMENT ON COLUMN public.blade_log_api.id IS '编号';
COMMENT ON COLUMN public.blade_log_api.tenant_id IS '租户ID';
COMMENT ON COLUMN public.blade_log_api.service_id IS '服务ID';
COMMENT ON COLUMN public.blade_log_api.server_host IS '服务器名';
COMMENT ON COLUMN public.blade_log_api.server_ip IS '服务器IP地址';
COMMENT ON COLUMN public.blade_log_api.env IS '服务器环境';
COMMENT ON COLUMN public.blade_log_api."type" IS '日志类型';
COMMENT ON COLUMN public.blade_log_api.title IS '日志标题';
COMMENT ON COLUMN public.blade_log_api."method" IS '操作方式';
COMMENT ON COLUMN public.blade_log_api.request_uri IS '请求URI';
COMMENT ON COLUMN public.blade_log_api.user_agent IS '用户代理';
COMMENT ON COLUMN public.blade_log_api.remote_ip IS '操作IP地址';
COMMENT ON COLUMN public.blade_log_api.method_class IS '方法类';
COMMENT ON COLUMN public.blade_log_api.method_name IS '方法名';
COMMENT ON COLUMN public.blade_log_api.params IS '操作提交的数据';
COMMENT ON COLUMN public.blade_log_api."time" IS '执行时间';
COMMENT ON COLUMN public.blade_log_api.create_by IS '创建者';
COMMENT ON COLUMN public.blade_log_api.create_time IS '创建时间';


-- public.blade_log_error definition

-- Drop table

-- DROP TABLE public.blade_log_error;

CREATE TABLE public.blade_log_error (
	id int8 NOT NULL, -- 编号
	tenant_id varchar(12) NULL, -- 租户ID
	service_id varchar(32) NULL, -- 服务ID
	server_host varchar(255) NULL, -- 服务器名
	server_ip varchar(255) NULL, -- 服务器IP地址
	env varchar(255) NULL, -- 系统环境
	"method" varchar(10) NULL, -- 操作方式
	request_uri varchar(255) NULL, -- 请求URI
	user_agent varchar(1000) NULL, -- 用户代理
	stack_trace text NULL, -- 堆栈
	exception_name varchar(255) NULL, -- 异常名
	message text NULL, -- 异常信息
	line_number int4 NULL, -- 错误行数
	remote_ip varchar(255) NULL, -- 操作IP地址
	method_class varchar(255) NULL, -- 方法类
	file_name varchar(1000) NULL, -- 文件名
	method_name varchar(255) NULL, -- 方法名
	params text NULL, -- 操作提交的数据
	create_by varchar(64) NULL, -- 创建者
	create_time timestamp(6) NULL, -- 创建时间
	CONSTRAINT blade_log_error_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.blade_log_error IS '错误日志表';

-- Column comments

COMMENT ON COLUMN public.blade_log_error.id IS '编号';
COMMENT ON COLUMN public.blade_log_error.tenant_id IS '租户ID';
COMMENT ON COLUMN public.blade_log_error.service_id IS '服务ID';
COMMENT ON COLUMN public.blade_log_error.server_host IS '服务器名';
COMMENT ON COLUMN public.blade_log_error.server_ip IS '服务器IP地址';
COMMENT ON COLUMN public.blade_log_error.env IS '系统环境';
COMMENT ON COLUMN public.blade_log_error."method" IS '操作方式';
COMMENT ON COLUMN public.blade_log_error.request_uri IS '请求URI';
COMMENT ON COLUMN public.blade_log_error.user_agent IS '用户代理';
COMMENT ON COLUMN public.blade_log_error.stack_trace IS '堆栈';
COMMENT ON COLUMN public.blade_log_error.exception_name IS '异常名';
COMMENT ON COLUMN public.blade_log_error.message IS '异常信息';
COMMENT ON COLUMN public.blade_log_error.line_number IS '错误行数';
COMMENT ON COLUMN public.blade_log_error.remote_ip IS '操作IP地址';
COMMENT ON COLUMN public.blade_log_error.method_class IS '方法类';
COMMENT ON COLUMN public.blade_log_error.file_name IS '文件名';
COMMENT ON COLUMN public.blade_log_error.method_name IS '方法名';
COMMENT ON COLUMN public.blade_log_error.params IS '操作提交的数据';
COMMENT ON COLUMN public.blade_log_error.create_by IS '创建者';
COMMENT ON COLUMN public.blade_log_error.create_time IS '创建时间';


-- public.blade_log_usual definition

-- Drop table

-- DROP TABLE public.blade_log_usual;

CREATE TABLE public.blade_log_usual (
	id int8 NOT NULL, -- 编号
	tenant_id varchar(12) NULL, -- 租户ID
	service_id varchar(32) NULL, -- 服务ID
	server_host varchar(255) NULL, -- 服务器名
	server_ip varchar(255) NULL, -- 服务器IP地址
	env varchar(255) NULL, -- 系统环境
	log_level varchar(10) NULL, -- 日志级别
	log_id varchar(100) NULL, -- 日志业务id
	log_data text NULL, -- 日志数据
	"method" varchar(10) NULL, -- 操作方式
	request_uri varchar(255) NULL, -- 请求URI
	remote_ip varchar(255) NULL, -- 操作IP地址
	method_class varchar(255) NULL, -- 方法类
	method_name varchar(255) NULL, -- 方法名
	user_agent varchar(1000) NULL, -- 用户代理
	params text NULL, -- 操作提交的数据
	create_by varchar(64) NULL, -- 创建者
	create_time timestamp(6) NULL, -- 创建时间
	CONSTRAINT blade_log_usual_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.blade_log_usual IS '通用日志表';

-- Column comments

COMMENT ON COLUMN public.blade_log_usual.id IS '编号';
COMMENT ON COLUMN public.blade_log_usual.tenant_id IS '租户ID';
COMMENT ON COLUMN public.blade_log_usual.service_id IS '服务ID';
COMMENT ON COLUMN public.blade_log_usual.server_host IS '服务器名';
COMMENT ON COLUMN public.blade_log_usual.server_ip IS '服务器IP地址';
COMMENT ON COLUMN public.blade_log_usual.env IS '系统环境';
COMMENT ON COLUMN public.blade_log_usual.log_level IS '日志级别';
COMMENT ON COLUMN public.blade_log_usual.log_id IS '日志业务id';
COMMENT ON COLUMN public.blade_log_usual.log_data IS '日志数据';
COMMENT ON COLUMN public.blade_log_usual."method" IS '操作方式';
COMMENT ON COLUMN public.blade_log_usual.request_uri IS '请求URI';
COMMENT ON COLUMN public.blade_log_usual.remote_ip IS '操作IP地址';
COMMENT ON COLUMN public.blade_log_usual.method_class IS '方法类';
COMMENT ON COLUMN public.blade_log_usual.method_name IS '方法名';
COMMENT ON COLUMN public.blade_log_usual.user_agent IS '用户代理';
COMMENT ON COLUMN public.blade_log_usual.params IS '操作提交的数据';
COMMENT ON COLUMN public.blade_log_usual.create_by IS '创建者';
COMMENT ON COLUMN public.blade_log_usual.create_time IS '创建时间';


-- public.blade_login_log definition

-- Drop table

-- DROP TABLE public.blade_login_log;

CREATE TABLE public.blade_login_log (
	id int8 NOT NULL, -- 编号
	name varchar(50) NULL, -- 姓名
	dept_name varchar(50) NULL, -- 部门名称
	login_name varchar(50) NULL, -- 登录名
	source_ip varchar(50) NULL, -- 来源IP地址
	start_time timestamp(6) NULL, -- 登录开始时间
	end_time timestamp(6) NULL, -- 登录结束时间
	"source" varchar(50) NULL, -- 登录方式
	create_time timestamp(6) NULL, -- 创建时间
	phone varchar(45) NULL, -- 手机号
	CONSTRAINT blade_login_log_pkey PRIMARY KEY (id)
);

-- Column comments

COMMENT ON COLUMN public.blade_login_log.id IS '编号';
COMMENT ON COLUMN public.blade_login_log.name IS '姓名';
COMMENT ON COLUMN public.blade_login_log.dept_name IS '部门名称';
COMMENT ON COLUMN public.blade_login_log.login_name IS '登录名';
COMMENT ON COLUMN public.blade_login_log.source_ip IS '来源IP地址';
COMMENT ON COLUMN public.blade_login_log.start_time IS '登录开始时间';
COMMENT ON COLUMN public.blade_login_log.end_time IS '登录结束时间';
COMMENT ON COLUMN public.blade_login_log."source" IS '登录方式';
COMMENT ON COLUMN public.blade_login_log.create_time IS '创建时间';
COMMENT ON COLUMN public.blade_login_log.phone IS '手机号';


-- public.blade_menu definition

-- Drop table

-- DROP TABLE public.blade_menu;

CREATE TABLE public.blade_menu (
	id int8 NOT NULL, -- 主键
	parent_id int8 NULL, -- 父级菜单
	code varchar(255) NULL, -- 菜单编号
	name varchar(255) NULL, -- 菜单名称
	alias varchar(255) NULL, -- 菜单别名
	"path" varchar(255) NULL, -- 请求地址（冗余字段，数据源为route-path，同步时需要更新）
	"source" varchar(255) NULL, -- 菜单资源
	sort int4 NULL, -- 排序
	category int4 NULL, -- 菜单类型
	"action" int4 NULL, -- 操作按钮类型
	is_open int4 NULL, -- 是否打开新页面
	component varchar(255) NULL, -- 组件地址
	remark varchar(255) NULL, -- 备注
	is_deleted int4 NULL, -- 是否已删除
	api_path varchar(255) NULL, -- 调用API路径
	api_method varchar(255) NULL, -- 类全路径+方法名
	api_name varchar(100) NULL, -- API名称
	route_id varchar(255) NULL DEFAULT NULL::character varying, -- 路由ID
	navigation_id int8 NULL, -- 导航配置ID
	"type" varchar(20) NULL, -- 类型
	json varchar(50000) NULL, -- 自定义json
	product_id int8 NULL, -- 产品id
	CONSTRAINT blade_menu_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.blade_menu IS '菜单表';

-- Column comments

COMMENT ON COLUMN public.blade_menu.id IS '主键';
COMMENT ON COLUMN public.blade_menu.parent_id IS '父级菜单';
COMMENT ON COLUMN public.blade_menu.code IS '菜单编号';
COMMENT ON COLUMN public.blade_menu.name IS '菜单名称';
COMMENT ON COLUMN public.blade_menu.alias IS '菜单别名';
COMMENT ON COLUMN public.blade_menu."path" IS '请求地址（冗余字段，数据源为route-path，同步时需要更新）';
COMMENT ON COLUMN public.blade_menu."source" IS '菜单资源';
COMMENT ON COLUMN public.blade_menu.sort IS '排序';
COMMENT ON COLUMN public.blade_menu.category IS '菜单类型';
COMMENT ON COLUMN public.blade_menu."action" IS '操作按钮类型';
COMMENT ON COLUMN public.blade_menu.is_open IS '是否打开新页面';
COMMENT ON COLUMN public.blade_menu.component IS '组件地址';
COMMENT ON COLUMN public.blade_menu.remark IS '备注';
COMMENT ON COLUMN public.blade_menu.is_deleted IS '是否已删除';
COMMENT ON COLUMN public.blade_menu.api_path IS '调用API路径';
COMMENT ON COLUMN public.blade_menu.api_method IS '类全路径+方法名';
COMMENT ON COLUMN public.blade_menu.api_name IS 'API名称';
COMMENT ON COLUMN public.blade_menu.route_id IS '路由ID';
COMMENT ON COLUMN public.blade_menu.navigation_id IS '导航配置ID';
COMMENT ON COLUMN public.blade_menu."type" IS '类型';
COMMENT ON COLUMN public.blade_menu.json IS '自定义json';
COMMENT ON COLUMN public.blade_menu.product_id IS '产品id';

-- public.blade_notice definition

-- Drop table

-- DROP TABLE public.blade_notice;

CREATE TABLE public.blade_notice (
	id int8 NOT NULL, -- 主键
	tenant_id varchar(12) NULL, -- 租户ID
	title varchar(255) NULL, -- 标题
	category int4 NULL, -- 类型
	release_time timestamp(6) NULL, -- 发布时间
	"content" varchar(2000) NULL, -- 内容
	create_user int8 NULL, -- 创建人
	create_dept int8 NULL, -- 创建部门
	create_time timestamp(6) NULL, -- 创建时间
	update_user int8 NULL, -- 修改人
	update_time timestamp(6) NULL, -- 修改时间
	status int4 NULL, -- 状态
	is_deleted int4 NULL, -- 是否已删除
	CONSTRAINT blade_notice_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.blade_notice IS '通知公告表';

-- Column comments

COMMENT ON COLUMN public.blade_notice.id IS '主键';
COMMENT ON COLUMN public.blade_notice.tenant_id IS '租户ID';
COMMENT ON COLUMN public.blade_notice.title IS '标题';
COMMENT ON COLUMN public.blade_notice.category IS '类型';
COMMENT ON COLUMN public.blade_notice.release_time IS '发布时间';
COMMENT ON COLUMN public.blade_notice."content" IS '内容';
COMMENT ON COLUMN public.blade_notice.create_user IS '创建人';
COMMENT ON COLUMN public.blade_notice.create_dept IS '创建部门';
COMMENT ON COLUMN public.blade_notice.create_time IS '创建时间';
COMMENT ON COLUMN public.blade_notice.update_user IS '修改人';
COMMENT ON COLUMN public.blade_notice.update_time IS '修改时间';
COMMENT ON COLUMN public.blade_notice.status IS '状态';
COMMENT ON COLUMN public.blade_notice.is_deleted IS '是否已删除';


-- public.blade_org definition

-- Drop table

-- DROP TABLE public.blade_org;

CREATE TABLE public.blade_org (
	id int8 NOT NULL, -- 主键
	tenant_id varchar(12) NULL, -- 租户ID
	parent_id int8 NULL, -- 父主键
	ancestors varchar(2000) NULL, -- 祖级列表
	org_category int4 NULL, -- 机构类型
	org_name varchar(45) NULL, -- 机构名
	full_name varchar(45) NULL, -- 机构全称 or 机构简称
	sort int4 NULL, -- 排序
	remark varchar(255) NULL, -- 备注
	is_deleted int4 NULL, -- 是否已删除
	create_user int8 NULL, -- 创建人
	create_dept int8 NULL, -- 创建部门
	create_time timestamp(6) NULL DEFAULT NULL::timestamp without time zone, -- 创建时间
	update_user int8 NULL, -- 修改人
	update_time timestamp(6) NULL DEFAULT NULL::timestamp without time zone, -- 修改时间
	status int4 NULL, -- 状态
	logo text NULL, -- 机构logo
	brief_intro varchar(1000) NULL, -- 机构简介
	province varchar(64) NULL, -- 省份
	city varchar(64) NULL, -- 市
	district varchar(64) NULL, -- 区
	street varchar(64) NULL, -- 街道
	detailed_address varchar(512) NULL, -- 详细地址（办公地址）
	longitude numeric(10, 7) NULL, -- 经度
	latitude numeric(10, 7) NULL, -- 纬度
	code varchar(255) NULL DEFAULT NULL::character varying, -- 编号
	register_address varchar(1000) NULL DEFAULT NULL::character varying, -- 注册地址
	legal_person varchar(255) NULL DEFAULT NULL::character varying, -- 法人
	business_license varchar(255) NULL DEFAULT NULL::character varying, -- 营业执照
	telephone varchar(255) NULL DEFAULT NULL::character varying, -- 联系电话
	enterprise_video text NULL, -- 企业视频
	product_url varchar(2000) NULL DEFAULT NULL::character varying, -- 默认产品的id
	CONSTRAINT blade_org_pkey PRIMARY KEY (id)
);

-- Column comments

COMMENT ON COLUMN public.blade_org.id IS '主键';
COMMENT ON COLUMN public.blade_org.tenant_id IS '租户ID';
COMMENT ON COLUMN public.blade_org.parent_id IS '父主键';
COMMENT ON COLUMN public.blade_org.ancestors IS '祖级列表';
COMMENT ON COLUMN public.blade_org.org_category IS '机构类型';
COMMENT ON COLUMN public.blade_org.org_name IS '机构名';
COMMENT ON COLUMN public.blade_org.full_name IS '机构全称 or 机构简称';
COMMENT ON COLUMN public.blade_org.sort IS '排序';
COMMENT ON COLUMN public.blade_org.remark IS '备注';
COMMENT ON COLUMN public.blade_org.is_deleted IS '是否已删除';
COMMENT ON COLUMN public.blade_org.create_user IS '创建人';
COMMENT ON COLUMN public.blade_org.create_dept IS '创建部门';
COMMENT ON COLUMN public.blade_org.create_time IS '创建时间';
COMMENT ON COLUMN public.blade_org.update_user IS '修改人';
COMMENT ON COLUMN public.blade_org.update_time IS '修改时间';
COMMENT ON COLUMN public.blade_org.status IS '状态';
COMMENT ON COLUMN public.blade_org.logo IS '机构logo';
COMMENT ON COLUMN public.blade_org.brief_intro IS '机构简介';
COMMENT ON COLUMN public.blade_org.province IS '省份';
COMMENT ON COLUMN public.blade_org.city IS '市';
COMMENT ON COLUMN public.blade_org.district IS '区';
COMMENT ON COLUMN public.blade_org.street IS '街道';
COMMENT ON COLUMN public.blade_org.detailed_address IS '详细地址（办公地址）';
COMMENT ON COLUMN public.blade_org.longitude IS '经度';
COMMENT ON COLUMN public.blade_org.latitude IS '纬度';
COMMENT ON COLUMN public.blade_org.code IS '编号';
COMMENT ON COLUMN public.blade_org.register_address IS '注册地址';
COMMENT ON COLUMN public.blade_org.legal_person IS '法人';
COMMENT ON COLUMN public.blade_org.business_license IS '营业执照';
COMMENT ON COLUMN public.blade_org.telephone IS '联系电话';
COMMENT ON COLUMN public.blade_org.enterprise_video IS '企业视频';
COMMENT ON COLUMN public.blade_org.product_url IS '默认产品的id';

-- public.blade_oss definition

-- Drop table

-- DROP TABLE public.blade_oss;

CREATE TABLE public.blade_oss (
	id int8 NOT NULL, -- 主键
	tenant_id varchar(12) NULL, -- 租户ID
	category int4 NULL, -- 分类
	oss_code varchar(32) NULL, -- 资源编号
	endpoint varchar(255) NULL, -- 资源地址
	access_key varchar(255) NULL, -- accessKey
	secret_key varchar(255) NULL, -- secretKey
	bucket_name varchar(255) NULL, -- 空间名
	app_id varchar(255) NULL, -- 应用ID
	region varchar(255) NULL, -- 地域简称
	remark varchar(255) NULL, -- 备注
	create_user int8 NULL, -- 创建人
	create_dept int8 NULL, -- 创建部门
	create_time timestamp(6) NULL, -- 创建时间
	update_user int8 NULL, -- 修改人
	update_time timestamp(6) NULL, -- 修改时间
	status int4 NULL, -- 状态
	is_deleted int4 NULL, -- 是否已删除
	CONSTRAINT blade_oss_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.blade_oss IS '对象存储表';

-- Column comments

COMMENT ON COLUMN public.blade_oss.id IS '主键';
COMMENT ON COLUMN public.blade_oss.tenant_id IS '租户ID';
COMMENT ON COLUMN public.blade_oss.category IS '分类';
COMMENT ON COLUMN public.blade_oss.oss_code IS '资源编号';
COMMENT ON COLUMN public.blade_oss.endpoint IS '资源地址';
COMMENT ON COLUMN public.blade_oss.access_key IS 'accessKey';
COMMENT ON COLUMN public.blade_oss.secret_key IS 'secretKey';
COMMENT ON COLUMN public.blade_oss.bucket_name IS '空间名';
COMMENT ON COLUMN public.blade_oss.app_id IS '应用ID';
COMMENT ON COLUMN public.blade_oss.region IS '地域简称';
COMMENT ON COLUMN public.blade_oss.remark IS '备注';
COMMENT ON COLUMN public.blade_oss.create_user IS '创建人';
COMMENT ON COLUMN public.blade_oss.create_dept IS '创建部门';
COMMENT ON COLUMN public.blade_oss.create_time IS '创建时间';
COMMENT ON COLUMN public.blade_oss.update_user IS '修改人';
COMMENT ON COLUMN public.blade_oss.update_time IS '修改时间';
COMMENT ON COLUMN public.blade_oss.status IS '状态';
COMMENT ON COLUMN public.blade_oss.is_deleted IS '是否已删除';


-- public.blade_param definition

-- Drop table

-- DROP TABLE public.blade_param;

CREATE TABLE public.blade_param (
	id int8 NOT NULL, -- 主键
	param_name varchar(50000) NULL, -- 参数名
	param_key varchar(50000) NULL, -- 参数键
	param_value varchar(50000) NULL, -- 参数值
	remark varchar(255) NULL, -- 备注
	create_user int8 NULL, -- 创建人
	create_dept int8 NULL, -- 创建部门
	create_time timestamp(6) NULL, -- 创建时间
	update_user int8 NULL, -- 修改人
	update_time timestamp(6) NULL, -- 修改时间
	status int4 NULL, -- 状态
	is_deleted int4 NULL, -- 是否已删除
	CONSTRAINT blade_param_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.blade_param IS '参数表';

-- Column comments

COMMENT ON COLUMN public.blade_param.id IS '主键';
COMMENT ON COLUMN public.blade_param.param_name IS '参数名';
COMMENT ON COLUMN public.blade_param.param_key IS '参数键';
COMMENT ON COLUMN public.blade_param.param_value IS '参数值';
COMMENT ON COLUMN public.blade_param.remark IS '备注';
COMMENT ON COLUMN public.blade_param.create_user IS '创建人';
COMMENT ON COLUMN public.blade_param.create_dept IS '创建部门';
COMMENT ON COLUMN public.blade_param.create_time IS '创建时间';
COMMENT ON COLUMN public.blade_param.update_user IS '修改人';
COMMENT ON COLUMN public.blade_param.update_time IS '修改时间';
COMMENT ON COLUMN public.blade_param.status IS '状态';
COMMENT ON COLUMN public.blade_param.is_deleted IS '是否已删除';


-- public.blade_post definition

-- Drop table

-- DROP TABLE public.blade_post;

CREATE TABLE public.blade_post (
	id int8 NOT NULL, -- 主键
	tenant_id varchar(12) NULL, -- 租户ID
	category int4 NULL, -- 岗位类型
	post_code varchar(64) NULL, -- 岗位编号
	post_name varchar(64) NULL, -- 岗位名称
	sort int4 NULL, -- 岗位排序
	remark varchar(255) NULL, -- 备注
	create_user int8 NULL, -- 创建人
	create_dept int8 NULL, -- 创建部门
	create_time timestamp(6) NULL, -- 创建时间
	update_user int8 NULL, -- 修改人
	update_time timestamp(6) NULL, -- 修改时间
	status int4 NULL, -- 状态
	is_deleted int4 NULL, -- 是否已删除
	role_id varchar(1000) NULL, -- 角色id 增加role_id字段，存储多个，以,隔开
	org_id int8 NULL, -- 机构id
	dept_id int8 NULL, -- 部门id（所属部门）
	leader_flag int4 NULL DEFAULT 0, -- 是否负责人（1是  0否）
	CONSTRAINT blade_post_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.blade_post IS '岗位表';

-- Column comments

COMMENT ON COLUMN public.blade_post.id IS '主键';
COMMENT ON COLUMN public.blade_post.tenant_id IS '租户ID';
COMMENT ON COLUMN public.blade_post.category IS '岗位类型';
COMMENT ON COLUMN public.blade_post.post_code IS '岗位编号';
COMMENT ON COLUMN public.blade_post.post_name IS '岗位名称';
COMMENT ON COLUMN public.blade_post.sort IS '岗位排序';
COMMENT ON COLUMN public.blade_post.remark IS '备注';
COMMENT ON COLUMN public.blade_post.create_user IS '创建人';
COMMENT ON COLUMN public.blade_post.create_dept IS '创建部门';
COMMENT ON COLUMN public.blade_post.create_time IS '创建时间';
COMMENT ON COLUMN public.blade_post.update_user IS '修改人';
COMMENT ON COLUMN public.blade_post.update_time IS '修改时间';
COMMENT ON COLUMN public.blade_post.status IS '状态';
COMMENT ON COLUMN public.blade_post.is_deleted IS '是否已删除';
COMMENT ON COLUMN public.blade_post.role_id IS '角色id 增加role_id字段，存储多个，以,隔开';
COMMENT ON COLUMN public.blade_post.org_id IS '机构id';
COMMENT ON COLUMN public.blade_post.dept_id IS '部门id（所属部门）';
COMMENT ON COLUMN public.blade_post.leader_flag IS '是否负责人（1是  0否）';


-- public.blade_process_leave definition

-- Drop table

-- DROP TABLE public.blade_process_leave;

CREATE TABLE public.blade_process_leave (
	id int8 NOT NULL, -- 编号
	process_definition_id varchar(64) NULL, -- 流程定义主键
	process_instance_id varchar(64) NULL, -- 流程实例主键
	start_time timestamp(6) NULL, -- 开始时间
	end_time timestamp(6) NULL, -- 结束时间
	reason varchar(255) NULL, -- 请假理由
	task_user varchar(255) NULL, -- 第一级审批人
	apply_time timestamp(6) NULL, -- 申请时间
	create_user int8 NULL, -- 创建人
	create_dept int8 NULL, -- 创建部门
	create_time timestamp(6) NULL, -- 创建时间
	update_user int8 NULL, -- 修改人
	update_time timestamp(6) NULL, -- 修改时间
	status int4 NULL, -- 状态
	is_deleted int4 NULL, -- 是否已删除
	CONSTRAINT blade_process_leave_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.blade_process_leave IS '流程请假业务表';

-- Column comments

COMMENT ON COLUMN public.blade_process_leave.id IS '编号';
COMMENT ON COLUMN public.blade_process_leave.process_definition_id IS '流程定义主键';
COMMENT ON COLUMN public.blade_process_leave.process_instance_id IS '流程实例主键';
COMMENT ON COLUMN public.blade_process_leave.start_time IS '开始时间';
COMMENT ON COLUMN public.blade_process_leave.end_time IS '结束时间';
COMMENT ON COLUMN public.blade_process_leave.reason IS '请假理由';
COMMENT ON COLUMN public.blade_process_leave.task_user IS '第一级审批人';
COMMENT ON COLUMN public.blade_process_leave.apply_time IS '申请时间';
COMMENT ON COLUMN public.blade_process_leave.create_user IS '创建人';
COMMENT ON COLUMN public.blade_process_leave.create_dept IS '创建部门';
COMMENT ON COLUMN public.blade_process_leave.create_time IS '创建时间';
COMMENT ON COLUMN public.blade_process_leave.update_user IS '修改人';
COMMENT ON COLUMN public.blade_process_leave.update_time IS '修改时间';
COMMENT ON COLUMN public.blade_process_leave.status IS '状态';
COMMENT ON COLUMN public.blade_process_leave.is_deleted IS '是否已删除';


-- public.blade_product definition

-- Drop table

-- DROP TABLE public.blade_product;

CREATE TABLE public.blade_product (
	id int8 NOT NULL, -- id
	name varchar(45) NULL, -- 产品名称
	thumb varchar(45) NULL, -- 缩略图
	code varchar(45) NULL, -- 产品编码
	tenant_id varchar(12) NULL DEFAULT NULL::character varying, -- 租户ID
	create_user int8 NULL, -- 创建人
	create_dept int8 NULL, -- 创建部门
	create_time timestamp(6) NULL DEFAULT NULL::timestamp without time zone, -- 创建时间
	update_user int8 NULL, -- 修改人
	update_time timestamp(6) NULL DEFAULT NULL::timestamp without time zone, -- 修改时间
	status int4 NULL, -- 状态
	is_deleted int4 NULL, -- 是否已删除
	CONSTRAINT blade_product_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.blade_product IS '产品表';

-- Column comments

COMMENT ON COLUMN public.blade_product.id IS 'id';
COMMENT ON COLUMN public.blade_product.name IS '产品名称';
COMMENT ON COLUMN public.blade_product.thumb IS '缩略图';
COMMENT ON COLUMN public.blade_product.code IS '产品编码';
COMMENT ON COLUMN public.blade_product.tenant_id IS '租户ID';
COMMENT ON COLUMN public.blade_product.create_user IS '创建人';
COMMENT ON COLUMN public.blade_product.create_dept IS '创建部门';
COMMENT ON COLUMN public.blade_product.create_time IS '创建时间';
COMMENT ON COLUMN public.blade_product.update_user IS '修改人';
COMMENT ON COLUMN public.blade_product.update_time IS '修改时间';
COMMENT ON COLUMN public.blade_product.status IS '状态';
COMMENT ON COLUMN public.blade_product.is_deleted IS '是否已删除';

-- public.blade_product_navigation definition

-- Drop table

-- DROP TABLE public.blade_product_navigation;

CREATE TABLE public.blade_product_navigation (
	id int8 NOT NULL, -- id
	navigation_type varchar(1) NULL, -- 导航栏类型
	json varchar(500) NULL, -- json
	headline varchar(500) NULL, -- 标题
	background_url varchar(500) NULL, -- 背景图片
	background_color varchar(500) NULL, -- 背景颜色
	icon_url varchar(500) NULL, -- 图标
	"sequence" int4 NULL, -- 序列
	"describe" varchar(500) NULL, -- 描述信息
	href varchar(500) NULL, -- 跳转地址
	href_type varchar(500) NULL, -- 链接类型
	layout varchar(500) NULL, -- 菜单布局
	hide varchar(1) NULL, -- 是否隐藏
	product_id int8 NULL, -- 产品ID
	tenant_id varchar(12) NULL DEFAULT NULL::character varying, -- 租户ID
	create_user int8 NULL, -- 创建人
	create_dept int8 NULL, -- 创建部门
	create_time timestamp(6) NULL DEFAULT NULL::timestamp without time zone, -- 创建时间
	update_user int8 NULL, -- 修改人
	update_time timestamp(6) NULL DEFAULT NULL::timestamp without time zone, -- 修改时间
	status int4 NULL, -- 状态
	is_deleted int4 NULL, -- 是否已删除
	style_type int4 NULL, -- 样式类型
	CONSTRAINT blade_product_navigation_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.blade_product_navigation IS '产品导航配置表';

-- Column comments

COMMENT ON COLUMN public.blade_product_navigation.id IS 'id';
COMMENT ON COLUMN public.blade_product_navigation.navigation_type IS '导航栏类型';
COMMENT ON COLUMN public.blade_product_navigation.json IS 'json';
COMMENT ON COLUMN public.blade_product_navigation.headline IS '标题';
COMMENT ON COLUMN public.blade_product_navigation.background_url IS '背景图片';
COMMENT ON COLUMN public.blade_product_navigation.background_color IS '背景颜色';
COMMENT ON COLUMN public.blade_product_navigation.icon_url IS '图标';
COMMENT ON COLUMN public.blade_product_navigation."sequence" IS '序列';
COMMENT ON COLUMN public.blade_product_navigation."describe" IS '描述信息';
COMMENT ON COLUMN public.blade_product_navigation.href IS '跳转地址';
COMMENT ON COLUMN public.blade_product_navigation.href_type IS '链接类型';
COMMENT ON COLUMN public.blade_product_navigation.layout IS '菜单布局';
COMMENT ON COLUMN public.blade_product_navigation.hide IS '是否隐藏';
COMMENT ON COLUMN public.blade_product_navigation.product_id IS '产品ID';
COMMENT ON COLUMN public.blade_product_navigation.tenant_id IS '租户ID';
COMMENT ON COLUMN public.blade_product_navigation.create_user IS '创建人';
COMMENT ON COLUMN public.blade_product_navigation.create_dept IS '创建部门';
COMMENT ON COLUMN public.blade_product_navigation.create_time IS '创建时间';
COMMENT ON COLUMN public.blade_product_navigation.update_user IS '修改人';
COMMENT ON COLUMN public.blade_product_navigation.update_time IS '修改时间';
COMMENT ON COLUMN public.blade_product_navigation.status IS '状态';
COMMENT ON COLUMN public.blade_product_navigation.is_deleted IS '是否已删除';
COMMENT ON COLUMN public.blade_product_navigation.style_type IS '样式类型';


-- public.blade_project_route definition

-- Drop table

-- DROP TABLE public.blade_project_route;

CREATE TABLE public.blade_project_route (
	id varchar(255) NOT NULL, -- id
	parent_id varchar(255) NULL, -- 父级id
	name varchar(500) NULL, -- 名称
	"path" varchar(500) NULL, -- 路径
	product_id int8 NULL, -- 产品ID
	"type" int2 NULL, -- 类型 1 工程 2 路由本体 3按钮
	tenant_id varchar(12) NULL DEFAULT NULL::character varying, -- 租户ID
	create_user int8 NULL, -- 创建人
	create_dept int8 NULL, -- 创建部门
	create_time timestamp(0) NULL DEFAULT NULL::timestamp without time zone, -- 创建时间
	update_user int8 NULL, -- 修改人
	update_time timestamp(0) NULL DEFAULT NULL::timestamp without time zone, -- 修改时间
	status int4 NULL, -- 状态
	is_deleted int4 NULL DEFAULT 0, -- 是否已删除
	code varchar(100) NULL, -- 菜单编号
	alias varchar(100) NULL, -- 菜单别名
	CONSTRAINT blade_product_route_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.blade_project_route IS '工程路由表';

-- Column comments

COMMENT ON COLUMN public.blade_project_route.id IS 'id';
COMMENT ON COLUMN public.blade_project_route.parent_id IS '父级id';
COMMENT ON COLUMN public.blade_project_route.name IS '名称';
COMMENT ON COLUMN public.blade_project_route."path" IS '路径';
COMMENT ON COLUMN public.blade_project_route.product_id IS '产品ID';
COMMENT ON COLUMN public.blade_project_route."type" IS '类型 1 工程 2 路由本体 3按钮';
COMMENT ON COLUMN public.blade_project_route.tenant_id IS '租户ID';
COMMENT ON COLUMN public.blade_project_route.create_user IS '创建人';
COMMENT ON COLUMN public.blade_project_route.create_dept IS '创建部门';
COMMENT ON COLUMN public.blade_project_route.create_time IS '创建时间';
COMMENT ON COLUMN public.blade_project_route.update_user IS '修改人';
COMMENT ON COLUMN public.blade_project_route.update_time IS '修改时间';
COMMENT ON COLUMN public.blade_project_route.status IS '状态';
COMMENT ON COLUMN public.blade_project_route.is_deleted IS '是否已删除';
COMMENT ON COLUMN public.blade_project_route.code IS '菜单编号';
COMMENT ON COLUMN public.blade_project_route.alias IS '菜单别名';

-- public.blade_region definition

-- Drop table

-- DROP TABLE public.blade_region;

CREATE TABLE public.blade_region (
	code varchar(12) NOT NULL, -- 区划编号
	parent_code varchar(12) NULL, -- 父区划编号
	ancestors varchar(255) NULL, -- 祖区划编号
	name varchar(32) NULL, -- 区划名称
	province_code varchar(12) NULL, -- 省级区划编号
	province_name varchar(32) NULL, -- 省级名称
	city_code varchar(12) NULL, -- 市级区划编号
	city_name varchar(32) NULL, -- 市级名称
	district_code varchar(12) NULL, -- 区级区划编号
	district_name varchar(32) NULL, -- 区级名称
	town_code varchar(12) NULL, -- 镇级区划编号
	town_name varchar(32) NULL, -- 镇级名称
	village_code varchar(12) NULL, -- 村级区划编号
	village_name varchar(32) NULL, -- 村级名称
	region_level int4 NULL, -- 层级
	sort int4 NULL, -- 排序
	remark varchar(255) NULL, -- 备注
	CONSTRAINT blade_region_pkey PRIMARY KEY (code)
);
COMMENT ON TABLE public.blade_region IS '行政区划表';

-- Column comments

COMMENT ON COLUMN public.blade_region.code IS '区划编号';
COMMENT ON COLUMN public.blade_region.parent_code IS '父区划编号';
COMMENT ON COLUMN public.blade_region.ancestors IS '祖区划编号';
COMMENT ON COLUMN public.blade_region.name IS '区划名称';
COMMENT ON COLUMN public.blade_region.province_code IS '省级区划编号';
COMMENT ON COLUMN public.blade_region.province_name IS '省级名称';
COMMENT ON COLUMN public.blade_region.city_code IS '市级区划编号';
COMMENT ON COLUMN public.blade_region.city_name IS '市级名称';
COMMENT ON COLUMN public.blade_region.district_code IS '区级区划编号';
COMMENT ON COLUMN public.blade_region.district_name IS '区级名称';
COMMENT ON COLUMN public.blade_region.town_code IS '镇级区划编号';
COMMENT ON COLUMN public.blade_region.town_name IS '镇级名称';
COMMENT ON COLUMN public.blade_region.village_code IS '村级区划编号';
COMMENT ON COLUMN public.blade_region.village_name IS '村级名称';
COMMENT ON COLUMN public.blade_region.region_level IS '层级';
COMMENT ON COLUMN public.blade_region.sort IS '排序';
COMMENT ON COLUMN public.blade_region.remark IS '备注';

-- public.blade_report_file definition

-- Drop table

-- DROP TABLE public.blade_report_file;

CREATE TABLE public.blade_report_file (
	id int8 NOT NULL, -- 主键
	name varchar(100) NOT NULL, -- 文件名
	"content" bytea NULL, -- 文件内容
	create_time timestamp(6) NOT NULL, -- 创建时间
	update_time timestamp(6) NOT NULL, -- 更新时间
	is_deleted int4 NULL, -- 是否已删除
	CONSTRAINT blade_report_file_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.blade_report_file IS '报表文件表';

-- Column comments

COMMENT ON COLUMN public.blade_report_file.id IS '主键';
COMMENT ON COLUMN public.blade_report_file.name IS '文件名';
COMMENT ON COLUMN public.blade_report_file."content" IS '文件内容';
COMMENT ON COLUMN public.blade_report_file.create_time IS '创建时间';
COMMENT ON COLUMN public.blade_report_file.update_time IS '更新时间';
COMMENT ON COLUMN public.blade_report_file.is_deleted IS '是否已删除';

-- public.blade_role definition

-- Drop table

-- DROP TABLE public.blade_role;

CREATE TABLE public.blade_role (
	id int8 NOT NULL, -- 主键
	tenant_id varchar(12) NULL, -- 租户ID
	parent_id int8 NULL, -- 父主键
	role_name varchar(255) NULL, -- 角色名
	sort int4 NULL, -- 排序
	role_alias varchar(255) NULL, -- 角色别名
	is_deleted int4 NULL, -- 是否已删除
	org_id varchar(255) NULL, -- 机构id(多个id用','分隔)
	type_id int8 NULL, -- 类型ID
	create_time timestamp(6) NULL DEFAULT NULL::timestamp without time zone, -- 创建时间
	remarks varchar(1000) NULL DEFAULT NULL::character varying, -- 备注
	CONSTRAINT blade_role_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.blade_role IS '角色表';

-- Column comments

COMMENT ON COLUMN public.blade_role.id IS '主键';
COMMENT ON COLUMN public.blade_role.tenant_id IS '租户ID';
COMMENT ON COLUMN public.blade_role.parent_id IS '父主键';
COMMENT ON COLUMN public.blade_role.role_name IS '角色名';
COMMENT ON COLUMN public.blade_role.sort IS '排序';
COMMENT ON COLUMN public.blade_role.role_alias IS '角色别名';
COMMENT ON COLUMN public.blade_role.is_deleted IS '是否已删除';
COMMENT ON COLUMN public.blade_role.org_id IS '机构id(多个id用'',''分隔)';
COMMENT ON COLUMN public.blade_role.type_id IS '类型ID';
COMMENT ON COLUMN public.blade_role.create_time IS '创建时间';
COMMENT ON COLUMN public.blade_role.remarks IS '备注';


-- public.blade_role_assignment definition

-- Drop table

-- DROP TABLE public.blade_role_assignment;

CREATE TABLE public.blade_role_assignment (
	id int8 NOT NULL, -- 主键
	tenant_id varchar(12) NULL, -- 租户ID
	type_id int8 NULL, -- 类型id
	role_id int8 NULL, -- 角色id
	role_type varchar(100) NULL, -- 角色分配类型
	org_id int8 NULL, -- 机构ID
	CONSTRAINT blade_role_assignment_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.blade_role_assignment IS '角色分配关联关系表';

-- Column comments

COMMENT ON COLUMN public.blade_role_assignment.id IS '主键';
COMMENT ON COLUMN public.blade_role_assignment.tenant_id IS '租户ID';
COMMENT ON COLUMN public.blade_role_assignment.type_id IS '类型id';
COMMENT ON COLUMN public.blade_role_assignment.role_id IS '角色id';
COMMENT ON COLUMN public.blade_role_assignment.role_type IS '角色分配类型';
COMMENT ON COLUMN public.blade_role_assignment.org_id IS '机构ID';


-- public.blade_role_menu definition

-- Drop table

-- DROP TABLE public.blade_role_menu;

CREATE TABLE public.blade_role_menu (
	id int8 NOT NULL, -- 主键
	menu_id int8 NULL, -- type为1时--blade_menu，type为2时,--blade_product
	role_id int8 NULL, -- 角色id
	"type" int2 NOT NULL DEFAULT 1, -- 类型  1-菜单 2-产品
	CONSTRAINT blade_role_menu_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.blade_role_menu IS '角色菜单关联表';

-- Column comments

COMMENT ON COLUMN public.blade_role_menu.id IS '主键';
COMMENT ON COLUMN public.blade_role_menu.menu_id IS 'type为1时--blade_menu，type为2时--blade_product';
COMMENT ON COLUMN public.blade_role_menu.role_id IS '角色id';
COMMENT ON COLUMN public.blade_role_menu."type" IS '类型  1-菜单 2-产品';

-- public.blade_role_scope definition

-- Drop table

-- DROP TABLE public.blade_role_scope;

CREATE TABLE public.blade_role_scope (
	id int8 NOT NULL, -- 主键
	scope_category int4 NULL, -- 权限类型(1:数据权限、2:接口权限)
	scope_id int8 NULL, -- 权限id
	role_id int8 NULL, -- 角色id
	CONSTRAINT blade_role_scope_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.blade_role_scope IS '角色数据权限关联表';

-- Column comments

COMMENT ON COLUMN public.blade_role_scope.id IS '主键';
COMMENT ON COLUMN public.blade_role_scope.scope_category IS '权限类型(1:数据权限、2:接口权限)';
COMMENT ON COLUMN public.blade_role_scope.scope_id IS '权限id';
COMMENT ON COLUMN public.blade_role_scope.role_id IS '角色id';


-- public.blade_scope_api definition

-- Drop table

-- DROP TABLE public.blade_scope_api;

CREATE TABLE public.blade_scope_api (
	id int8 NOT NULL, -- 主键
	menu_id int8 NULL, -- 菜单主键
	resource_code varchar(255) NULL, -- 资源编号
	scope_name varchar(255) NULL, -- 接口权限名
	scope_path varchar(255) NULL, -- 接口权限地址
	scope_type int4 NULL, -- 接口权限类型
	remark varchar(255) NULL, -- 接口权限备注
	create_user int8 NULL, -- 创建人
	create_dept int8 NULL, -- 创建部门
	create_time timestamp(6) NULL, -- 创建时间
	update_user int8 NULL, -- 修改人
	update_time timestamp(6) NULL, -- 修改时间
	status int4 NULL, -- 状态
	is_deleted int4 NULL, -- 是否已删除
	CONSTRAINT blade_scope_api_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.blade_scope_api IS '接口权限表';

-- Column comments

COMMENT ON COLUMN public.blade_scope_api.id IS '主键';
COMMENT ON COLUMN public.blade_scope_api.menu_id IS '菜单主键';
COMMENT ON COLUMN public.blade_scope_api.resource_code IS '资源编号';
COMMENT ON COLUMN public.blade_scope_api.scope_name IS '接口权限名';
COMMENT ON COLUMN public.blade_scope_api.scope_path IS '接口权限地址';
COMMENT ON COLUMN public.blade_scope_api.scope_type IS '接口权限类型';
COMMENT ON COLUMN public.blade_scope_api.remark IS '接口权限备注';
COMMENT ON COLUMN public.blade_scope_api.create_user IS '创建人';
COMMENT ON COLUMN public.blade_scope_api.create_dept IS '创建部门';
COMMENT ON COLUMN public.blade_scope_api.create_time IS '创建时间';
COMMENT ON COLUMN public.blade_scope_api.update_user IS '修改人';
COMMENT ON COLUMN public.blade_scope_api.update_time IS '修改时间';
COMMENT ON COLUMN public.blade_scope_api.status IS '状态';
COMMENT ON COLUMN public.blade_scope_api.is_deleted IS '是否已删除';


-- public.blade_scope_data definition

-- Drop table

-- DROP TABLE public.blade_scope_data;

CREATE TABLE public.blade_scope_data (
	id int8 NOT NULL, -- 主键
	menu_id int8 NULL, -- 菜单主键
	resource_code varchar(255) NULL, -- 资源编号
	scope_name varchar(255) NULL, -- 数据权限名称
	scope_field varchar(255) NULL, -- 数据权限字段
	scope_class varchar(500) NULL, -- 数据权限类名
	scope_column varchar(255) NULL, -- 数据权限字段
	scope_type int4 NULL, -- 数据权限类型
	scope_value varchar(2000) NULL, -- 数据权限值域
	remark varchar(255) NULL, -- 数据权限备注
	create_user int8 NULL, -- 创建人
	create_dept int8 NULL, -- 创建部门
	create_time timestamp(6) NULL, -- 创建时间
	update_user int8 NULL, -- 修改人
	update_time timestamp(6) NULL, -- 修改时间
	status int4 NULL, -- 状态
	is_deleted int4 NULL, -- 是否已删除
	CONSTRAINT blade_scope_data_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.blade_scope_data IS '数据权限表';

-- Column comments

COMMENT ON COLUMN public.blade_scope_data.id IS '主键';
COMMENT ON COLUMN public.blade_scope_data.menu_id IS '菜单主键';
COMMENT ON COLUMN public.blade_scope_data.resource_code IS '资源编号';
COMMENT ON COLUMN public.blade_scope_data.scope_name IS '数据权限名称';
COMMENT ON COLUMN public.blade_scope_data.scope_field IS '数据权限字段';
COMMENT ON COLUMN public.blade_scope_data.scope_class IS '数据权限类名';
COMMENT ON COLUMN public.blade_scope_data.scope_column IS '数据权限字段';
COMMENT ON COLUMN public.blade_scope_data.scope_type IS '数据权限类型';
COMMENT ON COLUMN public.blade_scope_data.scope_value IS '数据权限值域';
COMMENT ON COLUMN public.blade_scope_data.remark IS '数据权限备注';
COMMENT ON COLUMN public.blade_scope_data.create_user IS '创建人';
COMMENT ON COLUMN public.blade_scope_data.create_dept IS '创建部门';
COMMENT ON COLUMN public.blade_scope_data.create_time IS '创建时间';
COMMENT ON COLUMN public.blade_scope_data.update_user IS '修改人';
COMMENT ON COLUMN public.blade_scope_data.update_time IS '修改时间';
COMMENT ON COLUMN public.blade_scope_data.status IS '状态';
COMMENT ON COLUMN public.blade_scope_data.is_deleted IS '是否已删除';


-- public.blade_sms definition

-- Drop table

-- DROP TABLE public.blade_sms;

CREATE TABLE public.blade_sms (
	id int8 NOT NULL, -- 主键
	tenant_id varchar(12) NULL, -- 租户ID
	category int4 NULL, -- 分类
	sms_code varchar(12) NULL, -- 资源编号
	template_id varchar(64) NULL, -- 模板ID
	access_key varchar(255) NULL, -- accessKey
	secret_key varchar(255) NULL, -- secretKey
	region_id varchar(255) NULL, -- regionId
	sign_name varchar(64) NULL, -- 短信签名
	remark varchar(255) NULL, -- 备注
	create_user int8 NULL, -- 创建人
	create_dept int8 NULL, -- 创建部门
	create_time timestamp(6) NULL, -- 创建时间
	update_user int8 NULL, -- 修改人
	update_time timestamp(6) NULL, -- 修改时间
	status int4 NULL, -- 状态
	is_deleted int4 NULL, -- 是否已删除
	CONSTRAINT blade_sms_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.blade_sms IS '短信配置表';

-- Column comments

COMMENT ON COLUMN public.blade_sms.id IS '主键';
COMMENT ON COLUMN public.blade_sms.tenant_id IS '租户ID';
COMMENT ON COLUMN public.blade_sms.category IS '分类';
COMMENT ON COLUMN public.blade_sms.sms_code IS '资源编号';
COMMENT ON COLUMN public.blade_sms.template_id IS '模板ID';
COMMENT ON COLUMN public.blade_sms.access_key IS 'accessKey';
COMMENT ON COLUMN public.blade_sms.secret_key IS 'secretKey';
COMMENT ON COLUMN public.blade_sms.region_id IS 'regionId';
COMMENT ON COLUMN public.blade_sms.sign_name IS '短信签名';
COMMENT ON COLUMN public.blade_sms.remark IS '备注';
COMMENT ON COLUMN public.blade_sms.create_user IS '创建人';
COMMENT ON COLUMN public.blade_sms.create_dept IS '创建部门';
COMMENT ON COLUMN public.blade_sms.create_time IS '创建时间';
COMMENT ON COLUMN public.blade_sms.update_user IS '修改人';
COMMENT ON COLUMN public.blade_sms.update_time IS '修改时间';
COMMENT ON COLUMN public.blade_sms.status IS '状态';
COMMENT ON COLUMN public.blade_sms.is_deleted IS '是否已删除';


-- public.blade_tenant definition

-- Drop table

-- DROP TABLE public.blade_tenant;

CREATE TABLE public.blade_tenant (
	id int8 NOT NULL, -- 主键
	tenant_id varchar(12) NULL, -- 租户ID
	tenant_name varchar(50) NOT NULL, -- 租户名称
	"domain" varchar(255) NULL, -- 域名地址
	background_url varchar(1000) NULL, -- 系统背景
	linkman varchar(20) NULL, -- 联系人
	contact_number varchar(20) NULL, -- 联系电话
	address varchar(255) NULL, -- 联系地址
	account_number int2 NULL DEFAULT '-1'::integer, -- 账号额度
	expire_time timestamp(6) NULL, -- 过期时间
	package_id int8 NULL, -- 产品包ID
	datasource_id int8 NULL, -- 数据源ID
	license_key varchar(1000) NULL, -- 授权码
	create_user int8 NULL, -- 创建人
	create_dept int8 NULL, -- 创建部门
	create_time timestamp(6) NULL, -- 创建时间
	update_user int8 NULL, -- 修改人
	update_time timestamp(6) NULL, -- 修改时间
	status int4 NULL, -- 状态
	is_deleted int4 NULL, -- 是否已删除
	CONSTRAINT blade_tenant_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.blade_tenant IS '租户表';

-- Column comments

COMMENT ON COLUMN public.blade_tenant.id IS '主键';
COMMENT ON COLUMN public.blade_tenant.tenant_id IS '租户ID';
COMMENT ON COLUMN public.blade_tenant.tenant_name IS '租户名称';
COMMENT ON COLUMN public.blade_tenant."domain" IS '域名地址';
COMMENT ON COLUMN public.blade_tenant.background_url IS '系统背景';
COMMENT ON COLUMN public.blade_tenant.linkman IS '联系人';
COMMENT ON COLUMN public.blade_tenant.contact_number IS '联系电话';
COMMENT ON COLUMN public.blade_tenant.address IS '联系地址';
COMMENT ON COLUMN public.blade_tenant.account_number IS '账号额度';
COMMENT ON COLUMN public.blade_tenant.expire_time IS '过期时间';
COMMENT ON COLUMN public.blade_tenant.package_id IS '产品包ID';
COMMENT ON COLUMN public.blade_tenant.datasource_id IS '数据源ID';
COMMENT ON COLUMN public.blade_tenant.license_key IS '授权码';
COMMENT ON COLUMN public.blade_tenant.create_user IS '创建人';
COMMENT ON COLUMN public.blade_tenant.create_dept IS '创建部门';
COMMENT ON COLUMN public.blade_tenant.create_time IS '创建时间';
COMMENT ON COLUMN public.blade_tenant.update_user IS '修改人';
COMMENT ON COLUMN public.blade_tenant.update_time IS '修改时间';
COMMENT ON COLUMN public.blade_tenant.status IS '状态';
COMMENT ON COLUMN public.blade_tenant.is_deleted IS '是否已删除';


-- public.blade_tenant_package definition

-- Drop table

-- DROP TABLE public.blade_tenant_package;

CREATE TABLE public.blade_tenant_package (
	id int8 NOT NULL, -- 主键
	package_name varchar(50) NULL, -- 产品包名
	menu_id varchar(3000) NULL, -- 菜单ID
	remark varchar(255) NULL, -- 备注
	create_user int8 NULL, -- 创建人
	create_dept int8 NULL, -- 创建部门
	create_time timestamp(6) NULL, -- 创建时间
	update_user int8 NULL, -- 修改人
	update_time timestamp(6) NULL, -- 修改时间
	status int4 NULL, -- 状态
	is_deleted int4 NULL, -- 是否已删除
	CONSTRAINT blade_tenant_package_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.blade_tenant_package IS '租户产品表';

-- Column comments

COMMENT ON COLUMN public.blade_tenant_package.id IS '主键';
COMMENT ON COLUMN public.blade_tenant_package.package_name IS '产品包名';
COMMENT ON COLUMN public.blade_tenant_package.menu_id IS '菜单ID';
COMMENT ON COLUMN public.blade_tenant_package.remark IS '备注';
COMMENT ON COLUMN public.blade_tenant_package.create_user IS '创建人';
COMMENT ON COLUMN public.blade_tenant_package.create_dept IS '创建部门';
COMMENT ON COLUMN public.blade_tenant_package.create_time IS '创建时间';
COMMENT ON COLUMN public.blade_tenant_package.update_user IS '修改人';
COMMENT ON COLUMN public.blade_tenant_package.update_time IS '修改时间';
COMMENT ON COLUMN public.blade_tenant_package.status IS '状态';
COMMENT ON COLUMN public.blade_tenant_package.is_deleted IS '是否已删除';


-- public.blade_top_menu definition

-- Drop table

-- DROP TABLE public.blade_top_menu;

CREATE TABLE public.blade_top_menu (
	id int8 NOT NULL, -- 主键
	tenant_id varchar(12) NULL, -- 租户id
	code varchar(255) NULL, -- 顶部菜单编号
	name varchar(255) NULL, -- 顶部菜单名
	"source" varchar(255) NULL, -- 顶部菜单资源
	sort int4 NULL, -- 顶部菜单排序
	create_user int8 NULL, -- 创建人
	create_dept int8 NULL, -- 创建部门
	create_time timestamp(6) NULL, -- 创建时间
	update_user int8 NULL, -- 修改人
	update_time timestamp(6) NULL, -- 修改时间
	status int4 NULL, -- 状态
	is_deleted int4 NULL, -- 是否已删除
	CONSTRAINT blade_top_menu_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.blade_top_menu IS '顶部菜单表';

-- Column comments

COMMENT ON COLUMN public.blade_top_menu.id IS '主键';
COMMENT ON COLUMN public.blade_top_menu.tenant_id IS '租户id';
COMMENT ON COLUMN public.blade_top_menu.code IS '顶部菜单编号';
COMMENT ON COLUMN public.blade_top_menu.name IS '顶部菜单名';
COMMENT ON COLUMN public.blade_top_menu."source" IS '顶部菜单资源';
COMMENT ON COLUMN public.blade_top_menu.sort IS '顶部菜单排序';
COMMENT ON COLUMN public.blade_top_menu.create_user IS '创建人';
COMMENT ON COLUMN public.blade_top_menu.create_dept IS '创建部门';
COMMENT ON COLUMN public.blade_top_menu.create_time IS '创建时间';
COMMENT ON COLUMN public.blade_top_menu.update_user IS '修改人';
COMMENT ON COLUMN public.blade_top_menu.update_time IS '修改时间';
COMMENT ON COLUMN public.blade_top_menu.status IS '状态';
COMMENT ON COLUMN public.blade_top_menu.is_deleted IS '是否已删除';


-- public.blade_top_menu_setting definition

-- Drop table

-- DROP TABLE public.blade_top_menu_setting;

CREATE TABLE public.blade_top_menu_setting (
	id int8 NOT NULL, -- 主键
	top_menu_id int8 NULL, -- 顶部菜单主键
	menu_id int8 NULL, -- 菜单主键
	CONSTRAINT blade_top_menu_setting_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.blade_top_menu_setting IS '顶部菜单配置表';

-- Column comments

COMMENT ON COLUMN public.blade_top_menu_setting.id IS '主键';
COMMENT ON COLUMN public.blade_top_menu_setting.top_menu_id IS '顶部菜单主键';
COMMENT ON COLUMN public.blade_top_menu_setting.menu_id IS '菜单主键';


-- public.blade_user definition

-- Drop table

-- DROP TABLE public.blade_user;

CREATE TABLE public.blade_user (
	id int8 NOT NULL, -- 主键
	tenant_id varchar(12) NULL, -- 租户ID
	code varchar(12) NULL, -- 用户编号
	user_type int2 NULL, -- 用户平台
	account varchar(45) NULL, -- 账号
	"password" varchar(45) NULL, -- 密码
	name varchar(20) NULL, -- 昵称
	real_name varchar(20) NULL, -- 真名
	avatar varchar(500) NULL, -- 头像
	email varchar(45) NULL, -- 邮箱
	phone varchar(45) NULL, -- 手机
	birthday timestamp(6) NULL, -- 生日
	sex int2 NULL, -- 性别
	role_id varchar(1000) NULL, -- 角色id（多个id用','分割）
	dept_id varchar(1000) NULL, -- 部门id（多个id用','分割）
	post_id varchar(1000) NULL, -- 岗位id（多个id用','分割）
	create_user int8 NULL, -- 创建人
	create_dept int8 NULL, -- 创建部门
	create_time timestamp(6) NULL, -- 创建时间
	update_user int8 NULL, -- 修改人
	update_time timestamp(6) NULL, -- 修改时间
	status int4 NULL, -- 状态
	is_deleted int4 NULL, -- 是否已删除
	validity_start timestamp(0) NULL, -- 账号有效期（起）
	validity_end timestamp(0) NULL, -- 账号有效期（止）
	validity_type int4 NULL, -- 账号有效期类别（1：指定有效期，2：长期有效）
	org_id varchar(1000) NULL, -- 机构id（多个id用','分割）
	default_org int8 NULL, -- 默认机构
	pass_up_time timestamp(0) NULL, -- 密码修改时间
	org_name varchar(255) NULL, -- 机构名称
	CONSTRAINT blade_user_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.blade_user IS '用户表';

-- Column comments

COMMENT ON COLUMN public.blade_user.id IS '主键';
COMMENT ON COLUMN public.blade_user.tenant_id IS '租户ID';
COMMENT ON COLUMN public.blade_user.code IS '用户编号';
COMMENT ON COLUMN public.blade_user.user_type IS '用户平台';
COMMENT ON COLUMN public.blade_user.account IS '账号';
COMMENT ON COLUMN public.blade_user."password" IS '密码';
COMMENT ON COLUMN public.blade_user.name IS '昵称';
COMMENT ON COLUMN public.blade_user.real_name IS '真名';
COMMENT ON COLUMN public.blade_user.avatar IS '头像';
COMMENT ON COLUMN public.blade_user.email IS '邮箱';
COMMENT ON COLUMN public.blade_user.phone IS '手机';
COMMENT ON COLUMN public.blade_user.birthday IS '生日';
COMMENT ON COLUMN public.blade_user.sex IS '性别';
COMMENT ON COLUMN public.blade_user.role_id IS '角色id（多个id用'',''分割）';
COMMENT ON COLUMN public.blade_user.dept_id IS '部门id（多个id用'',''分割）';
COMMENT ON COLUMN public.blade_user.post_id IS '岗位id（多个id用'',''分割）';
COMMENT ON COLUMN public.blade_user.create_user IS '创建人';
COMMENT ON COLUMN public.blade_user.create_dept IS '创建部门';
COMMENT ON COLUMN public.blade_user.create_time IS '创建时间';
COMMENT ON COLUMN public.blade_user.update_user IS '修改人';
COMMENT ON COLUMN public.blade_user.update_time IS '修改时间';
COMMENT ON COLUMN public.blade_user.status IS '状态';
COMMENT ON COLUMN public.blade_user.is_deleted IS '是否已删除';
COMMENT ON COLUMN public.blade_user.validity_start IS '账号有效期（起）';
COMMENT ON COLUMN public.blade_user.validity_end IS '账号有效期（止）';
COMMENT ON COLUMN public.blade_user.validity_type IS '账号有效期类别（1：指定有效期，2：长期有效）';
COMMENT ON COLUMN public.blade_user.org_id IS '机构id（多个id用'',''分割）';
COMMENT ON COLUMN public.blade_user.default_org IS '默认机构';
COMMENT ON COLUMN public.blade_user.pass_up_time IS '密码修改时间';
COMMENT ON COLUMN public.blade_user.org_name IS '机构名称';


-- public.blade_user_app definition

-- Drop table

-- DROP TABLE public.blade_user_app;

CREATE TABLE public.blade_user_app (
	id int8 NOT NULL, -- 主键
	user_id int8 NULL, -- 用户ID
	user_ext varchar(255) NULL, -- 用户拓展信息
	CONSTRAINT blade_user_app_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.blade_user_app IS '用户平台拓展表';

-- Column comments

COMMENT ON COLUMN public.blade_user_app.id IS '主键';
COMMENT ON COLUMN public.blade_user_app.user_id IS '用户ID';
COMMENT ON COLUMN public.blade_user_app.user_ext IS '用户拓展信息';


-- public.blade_user_dept definition

-- Drop table

-- DROP TABLE public.blade_user_dept;

CREATE TABLE public.blade_user_dept (
	id int8 NOT NULL, -- 主键
	user_id int8 NULL, -- 用户ID
	dept_id int8 NULL, -- 部门ID
	org_id int8 NULL, -- 机构ID
	CONSTRAINT blade_user_dept_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.blade_user_dept IS '用户部门表';

-- Column comments

COMMENT ON COLUMN public.blade_user_dept.id IS '主键';
COMMENT ON COLUMN public.blade_user_dept.user_id IS '用户ID';
COMMENT ON COLUMN public.blade_user_dept.dept_id IS '部门ID';
COMMENT ON COLUMN public.blade_user_dept.org_id IS '机构ID';


-- public.blade_user_group definition

-- Drop table

-- DROP TABLE public.blade_user_group;

CREATE TABLE public.blade_user_group (
	id int8 NOT NULL, -- 主键
	name varchar(45) NULL, -- 名称
	description varchar(255) NULL, -- 描述
	org_id varchar(1000) NULL, -- 机构id
	role_id varchar(1000) NULL, -- 角色id
	create_user int8 NULL, -- 创建人
	create_dept int8 NULL, -- 创建部门
	create_time timestamp(6) NULL DEFAULT NULL::timestamp without time zone, -- 创建时间
	update_user int8 NULL, -- 修改人
	update_time timestamp(6) NULL DEFAULT NULL::timestamp without time zone, -- 修改时间
	status int4 NULL, -- 状态
	is_deleted int4 NULL, -- 是否已删除
	CONSTRAINT blade_user_group_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.blade_user_group IS '机构表';

-- Column comments

COMMENT ON COLUMN public.blade_user_group.id IS '主键';
COMMENT ON COLUMN public.blade_user_group.name IS '名称';
COMMENT ON COLUMN public.blade_user_group.description IS '描述';
COMMENT ON COLUMN public.blade_user_group.org_id IS '机构id';
COMMENT ON COLUMN public.blade_user_group.role_id IS '角色id';
COMMENT ON COLUMN public.blade_user_group.create_user IS '创建人';
COMMENT ON COLUMN public.blade_user_group.create_dept IS '创建部门';
COMMENT ON COLUMN public.blade_user_group.create_time IS '创建时间';
COMMENT ON COLUMN public.blade_user_group.update_user IS '修改人';
COMMENT ON COLUMN public.blade_user_group.update_time IS '修改时间';
COMMENT ON COLUMN public.blade_user_group.status IS '状态';
COMMENT ON COLUMN public.blade_user_group.is_deleted IS '是否已删除';


-- public.blade_user_oauth definition

-- Drop table

-- DROP TABLE public.blade_user_oauth;

CREATE TABLE public.blade_user_oauth (
	id int8 NOT NULL, -- 主键
	tenant_id varchar(12) NULL, -- 租户ID
	uuid varchar(64) NULL, -- 第三方系统用户ID
	user_id int8 NULL, -- 用户ID
	username varchar(32) NULL, -- 账号
	nickname varchar(64) NULL, -- 用户名
	avatar varchar(1000) NULL, -- 头像
	blog varchar(50) NULL, -- 应用主页
	company varchar(255) NULL, -- 公司名
	"location" varchar(255) NULL, -- 地址
	email varchar(255) NULL, -- 邮件
	remark varchar(255) NULL, -- 备注
	gender varchar(16) NULL, -- 性别
	"source" varchar(16) NULL, -- 来源
	CONSTRAINT blade_user_oauth_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.blade_user_oauth IS '用户第三方认证表';

-- Column comments

COMMENT ON COLUMN public.blade_user_oauth.id IS '主键';
COMMENT ON COLUMN public.blade_user_oauth.tenant_id IS '租户ID';
COMMENT ON COLUMN public.blade_user_oauth.uuid IS '第三方系统用户ID';
COMMENT ON COLUMN public.blade_user_oauth.user_id IS '用户ID';
COMMENT ON COLUMN public.blade_user_oauth.username IS '账号';
COMMENT ON COLUMN public.blade_user_oauth.nickname IS '用户名';
COMMENT ON COLUMN public.blade_user_oauth.avatar IS '头像';
COMMENT ON COLUMN public.blade_user_oauth.blog IS '应用主页';
COMMENT ON COLUMN public.blade_user_oauth.company IS '公司名';
COMMENT ON COLUMN public.blade_user_oauth."location" IS '地址';
COMMENT ON COLUMN public.blade_user_oauth.email IS '邮件';
COMMENT ON COLUMN public.blade_user_oauth.remark IS '备注';
COMMENT ON COLUMN public.blade_user_oauth.gender IS '性别';
COMMENT ON COLUMN public.blade_user_oauth."source" IS '来源';


-- public.blade_user_other definition

-- Drop table

-- DROP TABLE public.blade_user_other;

CREATE TABLE public.blade_user_other (
	id int8 NOT NULL, -- 主键
	user_id int8 NULL, -- 用户ID
	user_ext varchar(255) NULL, -- 用户拓展信息
	CONSTRAINT blade_user_other_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.blade_user_other IS '用户平台拓展表';

-- Column comments

COMMENT ON COLUMN public.blade_user_other.id IS '主键';
COMMENT ON COLUMN public.blade_user_other.user_id IS '用户ID';
COMMENT ON COLUMN public.blade_user_other.user_ext IS '用户拓展信息';


-- public.blade_user_post definition

-- Drop table

-- DROP TABLE public.blade_user_post;

CREATE TABLE public.blade_user_post (
	id int8 NOT NULL, -- 主键
	user_id int8 NULL, -- 用户ID
	post_id int8 NULL, -- 部门ID
	CONSTRAINT blade_user_post_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.blade_user_post IS '用户岗位表';

-- Column comments

COMMENT ON COLUMN public.blade_user_post.id IS '主键';
COMMENT ON COLUMN public.blade_user_post.user_id IS '用户ID';
COMMENT ON COLUMN public.blade_user_post.post_id IS '部门ID';


-- public.blade_user_web definition

-- Drop table

-- DROP TABLE public.blade_user_web;

CREATE TABLE public.blade_user_web (
	id int8 NOT NULL, -- 主键
	user_id int8 NULL, -- 用户ID
	user_ext varchar(255) NULL, -- 用户拓展信息
	CONSTRAINT blade_user_web_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.blade_user_web IS '用户平台拓展表';

-- Column comments

COMMENT ON COLUMN public.blade_user_web.id IS '主键';
COMMENT ON COLUMN public.blade_user_web.user_id IS '用户ID';
COMMENT ON COLUMN public.blade_user_web.user_ext IS '用户拓展信息';
