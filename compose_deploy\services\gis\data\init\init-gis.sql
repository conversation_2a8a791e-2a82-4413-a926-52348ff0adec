-- CREATE EXTENSION postgis;

-- ----------------------------
-- FUNCTION
-- ----------------------------
CREATE OR REPLACE FUNCTION "public"."update_point"()
RETURNS "pg_catalog"."trigger"
AS $BODY$
BEGIN
  IF NEW.geometry_point is NULL THEN
    EXECUTE format(
      'UPDATE %1$I set geolocation_area_id = NULL where %1$I.id = %2$L',
      TG_TABLE_NAME,
      NEW.id
    );
  ELSE
    EXECUTE format(
      'UPDATE %1$I set geolocation_area_id = (SELECT geolocation_area.id from %1$I CROSS JOIN geolocation_area where %1$I.id = %2$L and ST_Contains(geolocation_area.geometry_polygon, %1$I.geometry_point) limit 1) where %1$I.id = %2$L',
      TG_TABLE_NAME,
      NEW.id
    );
  END IF;
  RETURN NEW;
END;
$BODY$
LANGUAGE plpgsql
VOLATILE
COST 100;
ALTER FUNCTION "public"."update_point"() OWNER TO "postgres";

-- ----------------------------
-- Table structure for app_login
-- ----------------------------
DROP TABLE IF EXISTS "public"."app_login";
CREATE TABLE "public"."app_login" (
  "id" serial4 NOT NULL,
  "login_pic" text COLLATE "pg_catalog"."default",
  "login_title" text COLLATE "pg_catalog"."default",
  "mine_name" varchar(255) COLLATE "pg_catalog"."default"
);
COMMENT ON COLUMN "public"."app_login"."login_pic" IS '登录页图片';
COMMENT ON COLUMN "public"."app_login"."login_title" IS '登录页图标';
COMMENT ON COLUMN "public"."app_login"."mine_name" IS '矿井名字';

-- ----------------------------
-- Table structure for app_version_control
-- ----------------------------
DROP TABLE IF EXISTS "public"."app_version_control";
CREATE TABLE "public"."app_version_control" (
  "id" serial4 NOT NULL,
  "version" varchar(255) COLLATE "pg_catalog"."default",
  "description" text COLLATE "pg_catalog"."default",
  "create_time" timestamp(6)
);
COMMENT ON COLUMN "public"."app_version_control"."id" IS '主键';
COMMENT ON COLUMN "public"."app_version_control"."version" IS '版本号';
COMMENT ON COLUMN "public"."app_version_control"."description" IS '描述';
COMMENT ON COLUMN "public"."app_version_control"."create_time" IS '创建时间';
COMMENT ON TABLE "public"."app_version_control" IS 'APP版本控制';

-- ----------------------------
-- Table structure for bdata_alert
-- ----------------------------
DROP TABLE IF EXISTS "public"."bdata_alert";
CREATE TABLE "public"."bdata_alert" (
  "id" serial4 NOT NULL,
  "content" varchar(65535) COLLATE "pg_catalog"."default",
  "alert_time" timestamp(6),
  "indictor_id" int4
);
COMMENT ON COLUMN "public"."bdata_alert"."id" IS 'id';
COMMENT ON COLUMN "public"."bdata_alert"."content" IS '报警内容';
COMMENT ON COLUMN "public"."bdata_alert"."alert_time" IS '报警时间';
COMMENT ON COLUMN "public"."bdata_alert"."indictor_id" IS '指标ID';
COMMENT ON TABLE "public"."bdata_alert" IS '报警';

-- ----------------------------
-- Table structure for bdata_alert_indictor
-- ----------------------------
DROP TABLE IF EXISTS "public"."bdata_alert_indictor";
CREATE TABLE "public"."bdata_alert_indictor" (
  "id" serial4 NOT NULL,
  "name" varchar(255) COLLATE "pg_catalog"."default",
  "type_id" int4,
  "active" bool,
  "indictor" varchar(255) COLLATE "pg_catalog"."default",
  "condition" varchar(255) COLLATE "pg_catalog"."default",
  "oper" varchar(255) COLLATE "pg_catalog"."default",
  "reminder" varchar(255) COLLATE "pg_catalog"."default",
  "sms" bool,
  "severity" int2
);
COMMENT ON COLUMN "public"."bdata_alert_indictor"."id" IS 'id';
COMMENT ON COLUMN "public"."bdata_alert_indictor"."name" IS '名称';
COMMENT ON COLUMN "public"."bdata_alert_indictor"."type_id" IS '指标类型';
COMMENT ON COLUMN "public"."bdata_alert_indictor"."active" IS '是否开启';
COMMENT ON COLUMN "public"."bdata_alert_indictor"."indictor" IS '指标';
COMMENT ON COLUMN "public"."bdata_alert_indictor"."condition" IS '判断条件';
COMMENT ON COLUMN "public"."bdata_alert_indictor"."oper" IS '处理措施';
COMMENT ON COLUMN "public"."bdata_alert_indictor"."reminder" IS '提醒目标人';
COMMENT ON COLUMN "public"."bdata_alert_indictor"."sms" IS '是否短信通知';
COMMENT ON COLUMN "public"."bdata_alert_indictor"."severity" IS '报警严重度';
COMMENT ON TABLE "public"."bdata_alert_indictor" IS '报警指标库';

-- ----------------------------
-- Table structure for bdata_alert_indictor_type
-- ----------------------------
DROP TABLE IF EXISTS "public"."bdata_alert_indictor_type";
CREATE TABLE "public"."bdata_alert_indictor_type" (
  "id" serial4 NOT NULL,
  "alert_type" varchar(255) COLLATE "pg_catalog"."default",
  "name" varchar(255) COLLATE "pg_catalog"."default",
  "pid" int4
);
COMMENT ON COLUMN "public"."bdata_alert_indictor_type"."id" IS 'id';
COMMENT ON COLUMN "public"."bdata_alert_indictor_type"."alert_type" IS '报警类型';
COMMENT ON COLUMN "public"."bdata_alert_indictor_type"."name" IS '名称';
COMMENT ON COLUMN "public"."bdata_alert_indictor_type"."pid" IS '父id';
COMMENT ON TABLE "public"."bdata_alert_indictor_type" IS '报警指标类型树';

-- ----------------------------
-- Table structure for bdata_defect_db
-- ----------------------------
DROP TABLE IF EXISTS "public"."bdata_defect_db";
CREATE TABLE "public"."bdata_defect_db" (
  "id" serial4 NOT NULL,
  "level" varchar(255) COLLATE "pg_catalog"."default",
  "category" varchar(255) COLLATE "pg_catalog"."default",
  "name" varchar(65535) COLLATE "pg_catalog"."default",
  "rule" varchar(65535) COLLATE "pg_catalog"."default",
  "oper" varchar(65535) COLLATE "pg_catalog"."default"
);
COMMENT ON COLUMN "public"."bdata_defect_db"."id" IS 'id';
COMMENT ON COLUMN "public"."bdata_defect_db"."level" IS '隐患分级';
COMMENT ON COLUMN "public"."bdata_defect_db"."category" IS '隐患类别';
COMMENT ON COLUMN "public"."bdata_defect_db"."name" IS '隐患名称';
COMMENT ON COLUMN "public"."bdata_defect_db"."rule" IS '相关规定';
COMMENT ON COLUMN "public"."bdata_defect_db"."oper" IS '控制措施';
COMMENT ON TABLE "public"."bdata_defect_db" IS '隐患知识库';

-- ----------------------------
-- Table structure for bdata_diag_indictor
-- ----------------------------
DROP TABLE IF EXISTS "public"."bdata_diag_indictor";
CREATE TABLE "public"."bdata_diag_indictor" (
  "id" serial4 NOT NULL,
  "type" varchar(255) COLLATE "pg_catalog"."default",
  "target" varchar(65535) COLLATE "pg_catalog"."default",
  "level" varchar(65535) COLLATE "pg_catalog"."default",
  "danger_factor" varchar(65535) COLLATE "pg_catalog"."default",
  "according" varchar(65535) COLLATE "pg_catalog"."default",
  "oper" varchar(65535) COLLATE "pg_catalog"."default"
);
COMMENT ON COLUMN "public"."bdata_diag_indictor"."id" IS 'id';
COMMENT ON COLUMN "public"."bdata_diag_indictor"."type" IS '类别';
COMMENT ON COLUMN "public"."bdata_diag_indictor"."target" IS '诊断对象';
COMMENT ON COLUMN "public"."bdata_diag_indictor"."level" IS '影响程度';
COMMENT ON COLUMN "public"."bdata_diag_indictor"."danger_factor" IS '不安全因素';
COMMENT ON COLUMN "public"."bdata_diag_indictor"."according" IS '诊断依据';
COMMENT ON COLUMN "public"."bdata_diag_indictor"."oper" IS '处置措施';
COMMENT ON TABLE "public"."bdata_diag_indictor" IS '安全诊断专家指标库';

-- ----------------------------
-- Table structure for bdata_diag_result
-- ----------------------------
DROP TABLE IF EXISTS "public"."bdata_diag_result";
CREATE TABLE "public"."bdata_diag_result" (
  "id" serial4 NOT NULL,
  "risk" varchar(255) COLLATE "pg_catalog"."default",
  "reason" varchar(255) COLLATE "pg_catalog"."default",
  "opinion" varchar(255) COLLATE "pg_catalog"."default",
  "result" varchar(255) COLLATE "pg_catalog"."default"
);
COMMENT ON COLUMN "public"."bdata_diag_result"."id" IS 'id';
COMMENT ON COLUMN "public"."bdata_diag_result"."risk" IS '风险项';
COMMENT ON COLUMN "public"."bdata_diag_result"."reason" IS '风险原因';
COMMENT ON COLUMN "public"."bdata_diag_result"."opinion" IS '处理意见';
COMMENT ON COLUMN "public"."bdata_diag_result"."result" IS '处理结果';
COMMENT ON TABLE "public"."bdata_diag_result" IS '风险诊断结果';

-- ----------------------------
-- Table structure for bdata_oper_regulation
-- ----------------------------
DROP TABLE IF EXISTS "public"."bdata_oper_regulation";
CREATE TABLE "public"."bdata_oper_regulation" (
  "id" serial4 NOT NULL,
  "category" varchar(255) COLLATE "pg_catalog"."default",
  "worktype" varchar(255) COLLATE "pg_catalog"."default",
  "type" varchar(255) COLLATE "pg_catalog"."default",
  "rule" varchar(65535) COLLATE "pg_catalog"."default"
);
COMMENT ON COLUMN "public"."bdata_oper_regulation"."id" IS 'id';
COMMENT ON COLUMN "public"."bdata_oper_regulation"."category" IS '专业分类';
COMMENT ON COLUMN "public"."bdata_oper_regulation"."worktype" IS '工种';
COMMENT ON COLUMN "public"."bdata_oper_regulation"."type" IS '分类';
COMMENT ON COLUMN "public"."bdata_oper_regulation"."rule" IS '相关规定';
COMMENT ON TABLE "public"."bdata_oper_regulation" IS '操作规程';

-- ----------------------------
-- Table structure for bdata_safety_regulation
-- ----------------------------
DROP TABLE IF EXISTS "public"."bdata_safety_regulation";
CREATE TABLE "public"."bdata_safety_regulation" (
  "id" serial4 NOT NULL,
  "clause" varchar(255) COLLATE "pg_catalog"."default",
  "rule" varchar(65535) COLLATE "pg_catalog"."default"
);
COMMENT ON COLUMN "public"."bdata_safety_regulation"."id" IS 'id';
COMMENT ON COLUMN "public"."bdata_safety_regulation"."clause" IS '条款';
COMMENT ON COLUMN "public"."bdata_safety_regulation"."rule" IS '相关规定';
COMMENT ON TABLE "public"."bdata_safety_regulation" IS '安全规程';

-- ----------------------------
-- Table structure for bdata_triviolation_db
-- ----------------------------
DROP TABLE IF EXISTS "public"."bdata_triviolation_db";
CREATE TABLE "public"."bdata_triviolation_db" (
  "id" serial4 NOT NULL,
  "category" varchar(255) COLLATE "pg_catalog"."default",
  "nature" varchar(255) COLLATE "pg_catalog"."default",
  "term" varchar(65535) COLLATE "pg_catalog"."default",
  "rule" varchar(65535) COLLATE "pg_catalog"."default"
);
COMMENT ON COLUMN "public"."bdata_triviolation_db"."id" IS 'id';
COMMENT ON COLUMN "public"."bdata_triviolation_db"."category" IS '专业分类';
COMMENT ON COLUMN "public"."bdata_triviolation_db"."nature" IS '违章性质';
COMMENT ON COLUMN "public"."bdata_triviolation_db"."term" IS '标准术语';
COMMENT ON COLUMN "public"."bdata_triviolation_db"."rule" IS '相关规定';
COMMENT ON TABLE "public"."bdata_triviolation_db" IS '三违知识库';

-- ----------------------------
-- Table structure for emergency_accident_configure
-- ----------------------------
DROP TABLE IF EXISTS "public"."emergency_accident_configure";
CREATE TABLE "public"."emergency_accident_configure" (
  "id" serial4 NOT NULL,
  "configure" text COLLATE "pg_catalog"."default",
  "name" varchar(255) COLLATE "pg_catalog"."default",
  "explain" varchar(255) COLLATE "pg_catalog"."default",
  "acc_id" varchar(32) COLLATE "pg_catalog"."default",
  "is_delete" bool
);
COMMENT ON COLUMN "public"."emergency_accident_configure"."configure" IS '流程图配置信息';
COMMENT ON COLUMN "public"."emergency_accident_configure"."name" IS '流程图名称';
COMMENT ON COLUMN "public"."emergency_accident_configure"."explain" IS '流程图说明';
COMMENT ON COLUMN "public"."emergency_accident_configure"."acc_id" IS '类型关联id';
COMMENT ON TABLE "public"."emergency_accident_configure" IS '流程图配置信息表';

-- ----------------------------
-- Table structure for emergency_accident_img
-- ----------------------------
DROP TABLE IF EXISTS "public"."emergency_accident_img";
CREATE TABLE "public"."emergency_accident_img" (
  "id" serial4 NOT NULL,
  "img_name" varchar(255) COLLATE "pg_catalog"."default",
  "img_url" varchar COLLATE "pg_catalog"."default",
  "url" varchar COLLATE "pg_catalog"."default"
);
COMMENT ON COLUMN "public"."emergency_accident_img"."img_name" IS '图标名称';
COMMENT ON COLUMN "public"."emergency_accident_img"."img_url" IS '图标地址';
COMMENT ON COLUMN "public"."emergency_accident_img"."url" IS '图标地址';
COMMENT ON TABLE "public"."emergency_accident_img" IS '流程图图标库 表';

-- ----------------------------
-- Table structure for emergency_configure_mapping
-- ----------------------------
DROP TABLE IF EXISTS "public"."emergency_configure_mapping";
CREATE TABLE "public"."emergency_configure_mapping" (
  "id" int4 NOT NULL,
  "accident_type" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "accident_level" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "created_at" timestamptz(6) DEFAULT now(),
  "update_at" timestamptz(6) DEFAULT now()
);
COMMENT ON COLUMN "public"."emergency_configure_mapping"."id" IS '事故类型id';
COMMENT ON COLUMN "public"."emergency_configure_mapping"."accident_type" IS '事故类型';
COMMENT ON COLUMN "public"."emergency_configure_mapping"."accident_level" IS '事故级别';
COMMENT ON COLUMN "public"."emergency_configure_mapping"."created_at" IS '创建时间';
COMMENT ON COLUMN "public"."emergency_configure_mapping"."update_at" IS '修改时间';

-- ----------------------------
-- Table structure for gis_accident_file
-- ----------------------------
DROP TABLE IF EXISTS "public"."gis_accident_file";
CREATE TABLE "public"."gis_accident_file" (
  "id" serial4 NOT NULL,
  "file_name" varchar(255) COLLATE "pg_catalog"."default",
  "file_url" varchar(255) COLLATE "pg_catalog"."default",
  "accident_type" int4,
  "level" int4,
  "create_time" timestamp(6)
);
COMMENT ON COLUMN "public"."gis_accident_file"."file_name" IS '文件名称';
COMMENT ON COLUMN "public"."gis_accident_file"."file_url" IS '文件minio路径';
COMMENT ON COLUMN "public"."gis_accident_file"."accident_type" IS '事故类型';
COMMENT ON COLUMN "public"."gis_accident_file"."level" IS '级别';
COMMENT ON COLUMN "public"."gis_accident_file"."create_time" IS '添加时间';
COMMENT ON TABLE "public"."gis_accident_file" IS '事故文件配置';

-- ----------------------------
-- Table structure for gis_bind_build_point
-- ----------------------------
DROP TABLE IF EXISTS "public"."gis_bind_build_point";
CREATE TABLE "public"."gis_bind_build_point" (
  "id" serial4 NOT NULL,
  "geometry_type" int2,
  "type_id" int4,
  "marker_id" int4,
  "line_id" int4,
  "polygon_id" int4,
  "icon" jsonb,
  "level" varchar(255) COLLATE "pg_catalog"."default",
  "color" varchar(255) COLLATE "pg_catalog"."default",
  "central_point" jsonb,
  "name" varchar(255) COLLATE "pg_catalog"."default",
  "describe" varchar(4000) COLLATE "pg_catalog"."default",
  "geometry_coordinate" "public"."geometry",
  "coordinate" jsonb,
  "topo_id" varchar(255) COLLATE "pg_catalog"."default",
  "auto_id" varchar COLLATE "pg_catalog"."default",
  "insert_time" timestamp(6),
  "update_time" timestamp(6) DEFAULT now()
);
COMMENT ON COLUMN "public"."gis_bind_build_point"."id" IS '主键id';
COMMENT ON COLUMN "public"."gis_bind_build_point"."geometry_type" IS '1 点 2 线 3面';
COMMENT ON COLUMN "public"."gis_bind_build_point"."type_id" IS 'gis_entity_id实体类id';
COMMENT ON COLUMN "public"."gis_bind_build_point"."marker_id" IS '关联传感器id';
COMMENT ON COLUMN "public"."gis_bind_build_point"."line_id" IS '关联巷道id';
COMMENT ON COLUMN "public"."gis_bind_build_point"."polygon_id" IS '关联工作面id';
COMMENT ON COLUMN "public"."gis_bind_build_point"."icon" IS '图标';
COMMENT ON COLUMN "public"."gis_bind_build_point"."level" IS '等级';
COMMENT ON COLUMN "public"."gis_bind_build_point"."color" IS '颜色';
COMMENT ON COLUMN "public"."gis_bind_build_point"."central_point" IS '中心点';
COMMENT ON COLUMN "public"."gis_bind_build_point"."name" IS '名称';
COMMENT ON COLUMN "public"."gis_bind_build_point"."describe" IS '属性';
COMMENT ON COLUMN "public"."gis_bind_build_point"."geometry_coordinate" IS 'geometry类型坐标';
COMMENT ON COLUMN "public"."gis_bind_build_point"."coordinate" IS 'jsonb类型坐标';
COMMENT ON COLUMN "public"."gis_bind_build_point"."topo_id" IS '关联topo图的id';
COMMENT ON COLUMN "public"."gis_bind_build_point"."auto_id" IS '关联自动化子系统id';
COMMENT ON COLUMN "public"."gis_bind_build_point"."insert_time" IS '添加时间';
COMMENT ON COLUMN "public"."gis_bind_build_point"."update_time" IS '更新时间';
COMMENT ON TABLE "public"."gis_bind_build_point" IS '自建表的绑定';

-- ----------------------------
-- Table structure for gis_bind_build_point_sub
-- ----------------------------
DROP TABLE IF EXISTS "public"."gis_bind_build_point_sub";
CREATE TABLE "public"."gis_bind_build_point_sub" (
  "gis_bind_buid_id" int4,
  "upload_url" text COLLATE "pg_catalog"."default",
  "id" serial4 NOT NULL,
  "file_name" varchar(255) COLLATE "pg_catalog"."default",
  "create_time" timestamp(6),
  "is_compiled" int4,
  "dwg_id" varchar(255) COLLATE "pg_catalog"."default"
);
COMMENT ON COLUMN "public"."gis_bind_build_point_sub"."gis_bind_buid_id" IS '自建表id';
COMMENT ON COLUMN "public"."gis_bind_build_point_sub"."upload_url" IS '上传附件url';
COMMENT ON COLUMN "public"."gis_bind_build_point_sub"."id" IS '主键自增';
COMMENT ON COLUMN "public"."gis_bind_build_point_sub"."file_name" IS '上传附件名';
COMMENT ON COLUMN "public"."gis_bind_build_point_sub"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."gis_bind_build_point_sub"."is_compiled" IS '0--dwg文件未编译 1--dwg文件已编译';
COMMENT ON COLUMN "public"."gis_bind_build_point_sub"."dwg_id" IS 'dwgId';
COMMENT ON TABLE "public"."gis_bind_build_point_sub" IS '自建系统文件配置';

-- ----------------------------
-- Table structure for gis_bind_point
-- ----------------------------
DROP TABLE IF EXISTS "public"."gis_bind_point";
CREATE TABLE "public"."gis_bind_point" (
  "id" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "geometry_type" int2,
  "gis_type_subsystem" int4 NOT NULL,
  "geometry_coordinate" "public"."geometry",
  "coordinate" jsonb,
  "marker_id" int4,
  "line_id" int4,
  "polygon_id" int4,
  "topo_id" varchar(255) COLLATE "pg_catalog"."default",
  "auto_id" varchar COLLATE "pg_catalog"."default",
  "move_type" int2 NOT NULL DEFAULT 0,
  "insert_time" timestamp(6),
  "update_time" timestamp(6) DEFAULT now()
);
COMMENT ON COLUMN "public"."gis_bind_point"."id" IS 'point点id';
COMMENT ON COLUMN "public"."gis_bind_point"."geometry_type" IS '1点 2 线 3 面';
COMMENT ON COLUMN "public"."gis_bind_point"."gis_type_subsystem" IS '子系统id';
COMMENT ON COLUMN "public"."gis_bind_point"."geometry_coordinate" IS 'geometry坐标';
COMMENT ON COLUMN "public"."gis_bind_point"."coordinate" IS 'json坐标';
COMMENT ON COLUMN "public"."gis_bind_point"."marker_id" IS '关联传感器id';
COMMENT ON COLUMN "public"."gis_bind_point"."line_id" IS '关联线id';
COMMENT ON COLUMN "public"."gis_bind_point"."polygon_id" IS '关联工作面id';
COMMENT ON COLUMN "public"."gis_bind_point"."topo_id" IS '关联topo图id';
COMMENT ON COLUMN "public"."gis_bind_point"."auto_id" IS '关联综自子系统列表';
COMMENT ON COLUMN "public"."gis_bind_point"."move_type" IS '用于工作面日度回采自动进尺：0 不移动 1 固定长度移动  2 等比长度移动';
COMMENT ON COLUMN "public"."gis_bind_point"."insert_time" IS '添加数据时间';
COMMENT ON COLUMN "public"."gis_bind_point"."update_time" IS '更新数据时间';
COMMENT ON TABLE "public"."gis_bind_point" IS '绑点表';

-- ----------------------------
-- Table structure for gis_bind_point_sub
-- ----------------------------
DROP TABLE IF EXISTS "public"."gis_bind_point_sub";
CREATE TABLE "public"."gis_bind_point_sub" (
  "gis_bind_point_id" varchar COLLATE "pg_catalog"."default",
  "gis_type_subsystem" int4,
  "upload_url" text COLLATE "pg_catalog"."default",
  "id" serial4 NOT NULL,
  "file_name" varchar(255) COLLATE "pg_catalog"."default",
  "create_time" timestamp(6),
  "is_compiled" int4,
  "dwg_id" varchar(255) COLLATE "pg_catalog"."default"
);
COMMENT ON COLUMN "public"."gis_bind_point_sub"."gis_bind_point_id" IS '绑点表id';
COMMENT ON COLUMN "public"."gis_bind_point_sub"."gis_type_subsystem" IS '子系统id';
COMMENT ON COLUMN "public"."gis_bind_point_sub"."upload_url" IS '上传附件url';
COMMENT ON COLUMN "public"."gis_bind_point_sub"."id" IS '主键id';
COMMENT ON COLUMN "public"."gis_bind_point_sub"."file_name" IS '上传附件名';
COMMENT ON COLUMN "public"."gis_bind_point_sub"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."gis_bind_point_sub"."is_compiled" IS '0--dwg文件未编译 1--dwg文件已编译';
COMMENT ON COLUMN "public"."gis_bind_point_sub"."dwg_id" IS 'dwgId';
COMMENT ON TABLE "public"."gis_bind_point_sub" IS '接入系统文件配置';

-- ----------------------------
-- Table structure for gis_entity_class
-- ----------------------------
DROP TABLE IF EXISTS "public"."gis_entity_class";
CREATE TABLE "public"."gis_entity_class" (
  "id" serial4 NOT NULL,
  "entity_name" varchar(50) COLLATE "pg_catalog"."default",
  "entity_code" varchar(50) COLLATE "pg_catalog"."default",
  "entity_level" int4,
  "entity_remarks" text COLLATE "pg_catalog"."default",
  "pid" int4 NOT NULL,
  "level" int2 NOT NULL
);
COMMENT ON COLUMN "public"."gis_entity_class"."entity_name" IS '分类名称';
COMMENT ON COLUMN "public"."gis_entity_class"."entity_code" IS '分类编号';
COMMENT ON COLUMN "public"."gis_entity_class"."entity_level" IS 'gis图显示级别';
COMMENT ON COLUMN "public"."gis_entity_class"."entity_remarks" IS '备注';
COMMENT ON COLUMN "public"."gis_entity_class"."pid" IS '如果为0则是一级节点';
COMMENT ON COLUMN "public"."gis_entity_class"."level" IS '层级级别，方便查询(1,2,3,4,5)';
COMMENT ON TABLE "public"."gis_entity_class" IS '实体分类字典表';

-- ----------------------------
-- Table structure for gis_enum_escape_route
-- ----------------------------
DROP TABLE IF EXISTS "public"."gis_enum_escape_route";
CREATE TABLE "public"."gis_enum_escape_route" (
  "id" serial4 NOT NULL,
  "name" varchar(255) COLLATE "pg_catalog"."default",
  "sort_id" int4
);
COMMENT ON COLUMN "public"."gis_enum_escape_route"."id" IS '主键(自增)';
COMMENT ON COLUMN "public"."gis_enum_escape_route"."name" IS '避灾路线类型名称';
COMMENT ON COLUMN "public"."gis_enum_escape_route"."sort_id" IS '排序';
COMMENT ON TABLE "public"."gis_enum_escape_route" IS '避灾路线类型表';

-- ----------------------------
-- Table structure for gis_escape_route
-- ----------------------------
DROP TABLE IF EXISTS "public"."gis_escape_route";
CREATE TABLE "public"."gis_escape_route" (
  "id" serial4 NOT NULL,
  "name" varchar(255) COLLATE "pg_catalog"."default",
  "end_name" varchar(255) COLLATE "pg_catalog"."default",
  "coordinate" jsonb,
  "central_point" jsonb,
  "escape_type" int2,
  "color" varchar(255) COLLATE "pg_catalog"."default",
  "radius" int2,
  "redundancy" jsonb
);
COMMENT ON COLUMN "public"."gis_escape_route"."id" IS '自增id';
COMMENT ON COLUMN "public"."gis_escape_route"."name" IS '避灾路线名称';
COMMENT ON COLUMN "public"."gis_escape_route"."end_name" IS '避灾路线结束位置名称';
COMMENT ON COLUMN "public"."gis_escape_route"."coordinate" IS '坐标';
COMMENT ON COLUMN "public"."gis_escape_route"."central_point" IS '中心点坐标';
COMMENT ON COLUMN "public"."gis_escape_route"."escape_type" IS '避灾类型';
COMMENT ON COLUMN "public"."gis_escape_route"."color" IS '颜色';
COMMENT ON COLUMN "public"."gis_escape_route"."radius" IS '吸附半径';
COMMENT ON COLUMN "public"."gis_escape_route"."redundancy" IS '预留字段';
COMMENT ON TABLE "public"."gis_escape_route" IS '避灾路线实体表';

-- ----------------------------
-- Table structure for gis_heading_day_record
-- ----------------------------
DROP TABLE IF EXISTS "public"."gis_heading_day_record";
CREATE TABLE "public"."gis_heading_day_record" (
  "heading_time" timestamp(6) NOT NULL,
  "heading_id" int4 NOT NULL,
  "heading_upper_chute" float4
);
COMMENT ON COLUMN "public"."gis_heading_day_record"."heading_time" IS '日度掘进时间';
COMMENT ON COLUMN "public"."gis_heading_day_record"."heading_id" IS '掘进面';
COMMENT ON COLUMN "public"."gis_heading_day_record"."heading_upper_chute" IS '掘进面开采进度,单位米';
COMMENT ON TABLE "public"."gis_heading_day_record" IS '掘进工作面的每日进度';

-- ----------------------------
-- Table structure for gis_mainpage
-- ----------------------------
DROP TABLE IF EXISTS "public"."gis_mainpage";
CREATE TABLE "public"."gis_mainpage" (
  "id" serial4 NOT NULL,
  "name" varchar(255) COLLATE "pg_catalog"."default",
  "geo_point" jsonb,
  "geo_polygon" jsonb,
  "focus" bool NOT NULL,
  "city_code" varchar(255) COLLATE "pg_catalog"."default",
  "description" varchar(4000) COLLATE "pg_catalog"."default",
  "roadway_map_url" varchar(255) COLLATE "pg_catalog"."default",
  "mine_map_url" varchar(255) COLLATE "pg_catalog"."default",
  "geo_line" jsonb,
  "city_sec_code" varchar(255) COLLATE "pg_catalog"."default",
  "view_business_data" bool,
  "center_point" jsonb,
  "level" int2 DEFAULT 1
);
COMMENT ON COLUMN "public"."gis_mainpage"."id" IS '自增ID';
COMMENT ON COLUMN "public"."gis_mainpage"."name" IS '名称';
COMMENT ON COLUMN "public"."gis_mainpage"."geo_point" IS '互联网坐标';
COMMENT ON COLUMN "public"."gis_mainpage"."geo_polygon" IS '煤矿周界';
COMMENT ON COLUMN "public"."gis_mainpage"."focus" IS 'false为周边矿井，true为主矿井(true只有一条记录)';
COMMENT ON COLUMN "public"."gis_mainpage"."city_code" IS '城市编码，为获取天气、互联网地图位置等使用';
COMMENT ON COLUMN "public"."gis_mainpage"."description" IS '矿信息描述';
COMMENT ON COLUMN "public"."gis_mainpage"."roadway_map_url" IS '巷道图地址';
COMMENT ON COLUMN "public"."gis_mainpage"."mine_map_url" IS '矿井图片';
COMMENT ON COLUMN "public"."gis_mainpage"."geo_line" IS '煤矿周界';
COMMENT ON COLUMN "public"."gis_mainpage"."city_sec_code" IS '城市编码';
COMMENT ON COLUMN "public"."gis_mainpage"."view_business_data" IS '展示数首页数据,默认为true';
COMMENT ON COLUMN "public"."gis_mainpage"."center_point" IS '中心点坐标';
COMMENT ON COLUMN "public"."gis_mainpage"."level" IS '级别(默认值:1)';
COMMENT ON TABLE "public"."gis_mainpage" IS 'GIS互联网首页信息配置表';

-- ----------------------------
-- Table structure for gis_map_enum
-- ----------------------------
DROP TABLE IF EXISTS "public"."gis_map_enum";
CREATE TABLE "public"."gis_map_enum" (
  "id" int4 NOT NULL,
  "pid" int4 NOT NULL,
  "city_code" varchar(255) COLLATE "pg_catalog"."default",
  "city_name" varchar(255) COLLATE "pg_catalog"."default",
  "post_code" varchar(255) COLLATE "pg_catalog"."default",
  "area_code" varchar(255) COLLATE "pg_catalog"."default"
);
COMMENT ON COLUMN "public"."gis_map_enum"."city_code" IS '城市编码';
COMMENT ON COLUMN "public"."gis_map_enum"."city_name" IS '城市名称';
COMMENT ON COLUMN "public"."gis_map_enum"."area_code" IS '区域编码';
COMMENT ON TABLE "public"."gis_map_enum" IS '城市基础配置';

-- ----------------------------
-- Table structure for gis_map_info
-- ----------------------------
DROP TABLE IF EXISTS "public"."gis_map_info";
CREATE TABLE "public"."gis_map_info" (
  "id" serial4 NOT NULL,
  "name" varchar(255) COLLATE "pg_catalog"."default",
  "gis_scheme_id" varchar(64) COLLATE "pg_catalog"."default",
  "brief" varchar(255) COLLATE "pg_catalog"."default",
  "description" varchar(255) COLLATE "pg_catalog"."default",
  "order_id" int2 NOT NULL,
  "pid" int4,
  "type" varchar(32) COLLATE "pg_catalog"."default"
);
COMMENT ON COLUMN "public"."gis_map_info"."id" IS 'pk';
COMMENT ON COLUMN "public"."gis_map_info"."name" IS '一张图名称';
COMMENT ON COLUMN "public"."gis_map_info"."gis_scheme_id" IS '专题图ID，用于映射是矢量协同服务的专题图信息';
COMMENT ON COLUMN "public"."gis_map_info"."brief" IS '图业务简短描述';
COMMENT ON COLUMN "public"."gis_map_info"."description" IS '不同图业务有不同的数据回显，需要形成模板化的文字，然后进行封装向前端输出';
COMMENT ON COLUMN "public"."gis_map_info"."order_id" IS '排序';
COMMENT ON COLUMN "public"."gis_map_info"."pid" IS '父类id';
COMMENT ON COLUMN "public"."gis_map_info"."type" IS '唯一标识';
COMMENT ON TABLE "public"."gis_map_info" IS '一张图基本信息配置表';

-- ----------------------------
-- Table structure for gis_map_info_content
-- ----------------------------
DROP TABLE IF EXISTS "public"."gis_map_info_content";
CREATE TABLE "public"."gis_map_info_content" (
  "id" serial4 NOT NULL,
  "name" varchar(255) COLLATE "pg_catalog"."default",
  "icon" text COLLATE "pg_catalog"."default",
  "pid" int4 NOT NULL DEFAULT 0,
  "type" varchar(255) COLLATE "pg_catalog"."default",
  "code" int4
);
COMMENT ON COLUMN "public"."gis_map_info_content"."id" IS '主键';
COMMENT ON COLUMN "public"."gis_map_info_content"."name" IS '名字';
COMMENT ON COLUMN "public"."gis_map_info_content"."icon" IS '图标';
COMMENT ON COLUMN "public"."gis_map_info_content"."pid" IS '父类id';
COMMENT ON COLUMN "public"."gis_map_info_content"."type" IS 'system 自建  general 数据接入';
COMMENT ON COLUMN "public"."gis_map_info_content"."code" IS '系统编号';
COMMENT ON TABLE "public"."gis_map_info_content" IS 'gis一张图展示内容配置表';

-- ----------------------------
-- Table structure for gis_map_info_content_category
-- ----------------------------
DROP TABLE IF EXISTS "public"."gis_map_info_content_category";
CREATE TABLE "public"."gis_map_info_content_category" (
  "system_id" int4 NOT NULL,
  "name" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "icon" text COLLATE "pg_catalog"."default",
  "sign" varchar(255) COLLATE "pg_catalog"."default",
  "system_type" varchar(255) COLLATE "pg_catalog"."default" NOT NULL
);
COMMENT ON COLUMN "public"."gis_map_info_content_category"."system_id" IS '系统id';
COMMENT ON COLUMN "public"."gis_map_info_content_category"."name" IS '分类名字';
COMMENT ON COLUMN "public"."gis_map_info_content_category"."icon" IS '图标';
COMMENT ON COLUMN "public"."gis_map_info_content_category"."sign" IS '标志:0 不可修改   1 可修改';
COMMENT ON COLUMN "public"."gis_map_info_content_category"."system_type" IS '系统标志';
COMMENT ON TABLE "public"."gis_map_info_content_category" IS 'gis一张图展示内容数据源配置';

-- ----------------------------
-- Table structure for gis_map_info_mapping_map_layer
-- ----------------------------
DROP TABLE IF EXISTS "public"."gis_map_info_mapping_map_layer";
CREATE TABLE "public"."gis_map_info_mapping_map_layer" (
  "id" serial4 NOT NULL,
  "map_info_id" int4 NOT NULL,
  "layer_id" varchar(255) COLLATE "pg_catalog"."default",
  "create_time" timestamp(6)
);
COMMENT ON COLUMN "public"."gis_map_info_mapping_map_layer"."id" IS '主键id';
COMMENT ON COLUMN "public"."gis_map_info_mapping_map_layer"."map_info_id" IS '一张图一级菜单id';
COMMENT ON COLUMN "public"."gis_map_info_mapping_map_layer"."layer_id" IS '图层id';
COMMENT ON COLUMN "public"."gis_map_info_mapping_map_layer"."create_time" IS '创建时间';
COMMENT ON TABLE "public"."gis_map_info_mapping_map_layer" IS '菜单和图层的映射关系';

-- ----------------------------
-- Table structure for gis_map_subbiz
-- ----------------------------
DROP TABLE IF EXISTS "public"."gis_map_subbiz";
CREATE TABLE "public"."gis_map_subbiz" (
  "id" serial4 NOT NULL,
  "name" varchar(255) COLLATE "pg_catalog"."default",
  "type" varchar(255) COLLATE "pg_catalog"."default",
  "build_type_id" varchar COLLATE "pg_catalog"."default"
);
COMMENT ON COLUMN "public"."gis_map_subbiz"."id" IS '自增id';
COMMENT ON COLUMN "public"."gis_map_subbiz"."name" IS '子业务名称';
COMMENT ON COLUMN "public"."gis_map_subbiz"."type" IS '前端业务标识';
COMMENT ON COLUMN "public"."gis_map_subbiz"."build_type_id" IS '关联自建系统的id';
COMMENT ON TABLE "public"."gis_map_subbiz" IS '一张图业务各个子业务模块名称列表';

-- ----------------------------
-- Table structure for gis_map_subbiz_conf
-- ----------------------------
DROP TABLE IF EXISTS "public"."gis_map_subbiz_conf";
CREATE TABLE "public"."gis_map_subbiz_conf" (
  "map_id" int4 NOT NULL,
  "subbiz_id" int4 NOT NULL
);
COMMENT ON COLUMN "public"."gis_map_subbiz_conf"."map_id" IS '一张图基本信息配置表 外键';
COMMENT ON COLUMN "public"."gis_map_subbiz_conf"."subbiz_id" IS '一张图业务各个子业务模块名称列表 表外键';
COMMENT ON TABLE "public"."gis_map_subbiz_conf" IS '一张图业务与各个子业务模块映射表';

-- ----------------------------
-- Table structure for gis_mapping_content
-- ----------------------------
DROP TABLE IF EXISTS "public"."gis_mapping_content";
CREATE TABLE "public"."gis_mapping_content" (
  "id" serial4 NOT NULL,
  "gis_map_info_id" int4,
  "gis_map_info_content_id" int4
);
COMMENT ON COLUMN "public"."gis_mapping_content"."gis_map_info_id" IS 'gis一张图基础内容配置表id';
COMMENT ON COLUMN "public"."gis_mapping_content"."gis_map_info_content_id" IS 'gis一张图内容配置表id';
COMMENT ON TABLE "public"."gis_mapping_content" IS 'gis一张图菜单和内容映射表';

-- ----------------------------
-- Table structure for gis_mining_day_record
-- ----------------------------
DROP TABLE IF EXISTS "public"."gis_mining_day_record";
CREATE TABLE "public"."gis_mining_day_record" (
  "mining_time" timestamp(6) NOT NULL,
  "mining_id" int4 NOT NULL,
  "mining_upper_chute" float4,
  "mining_lower_chute" float4
);
COMMENT ON COLUMN "public"."gis_mining_day_record"."mining_time" IS '日度回采时间';
COMMENT ON COLUMN "public"."gis_mining_day_record"."mining_id" IS '掘进面';
COMMENT ON COLUMN "public"."gis_mining_day_record"."mining_upper_chute" IS '上顺槽开采进度';
COMMENT ON COLUMN "public"."gis_mining_day_record"."mining_lower_chute" IS '下顺槽开采进度';
COMMENT ON TABLE "public"."gis_mining_day_record" IS '回采工作面的每日进度';

-- ----------------------------
-- Records of gis_mining_day_record
-- ----------------------------
-- ----------------------------
-- Table structure for gis_position_entity
-- ----------------------------
DROP TABLE IF EXISTS "public"."gis_position_entity";
CREATE TABLE "public"."gis_position_entity" (
  "id" serial4 NOT NULL,
  "station_code" varchar(50) COLLATE "pg_catalog"."default",
  "entity_id" int4
);
COMMENT ON COLUMN "public"."gis_position_entity"."id" IS '自增主键';
COMMENT ON COLUMN "public"."gis_position_entity"."station_code" IS '基站编码';
COMMENT ON COLUMN "public"."gis_position_entity"."entity_id" IS '实体id';
COMMENT ON TABLE "public"."gis_position_entity" IS '人员基站与实体工作面的关联表';

-- ----------------------------
-- Table structure for gis_public_config
-- ----------------------------
DROP TABLE IF EXISTS "public"."gis_public_config";
CREATE TABLE "public"."gis_public_config" (
  "id" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "value" jsonb
);
COMMENT ON COLUMN "public"."gis_public_config"."id" IS '唯一值';
COMMENT ON COLUMN "public"."gis_public_config"."value" IS '大json';
COMMENT ON TABLE "public"."gis_public_config" IS '公有配置表';

-- ----------------------------
-- Table structure for gis_rename_bind
-- ----------------------------
DROP TABLE IF EXISTS "public"."gis_rename_bind";
CREATE TABLE "public"."gis_rename_bind" (
  "id" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "gis_type_subsystem" int4 NOT NULL,
  "shortname" varchar(255) COLLATE "pg_catalog"."default"
);
COMMENT ON COLUMN "public"."gis_rename_bind"."id" IS '子系统ID，包含咱们得子系统名称';
COMMENT ON COLUMN "public"."gis_rename_bind"."gis_type_subsystem" IS 'gis_subsystem 字典表类型';
COMMENT ON COLUMN "public"."gis_rename_bind"."shortname" IS '简称名称';
COMMENT ON TABLE "public"."gis_rename_bind" IS '绑点名称特殊配置';

-- ----------------------------
-- Table structure for gis_safe_risk_dictionary
-- ----------------------------
DROP TABLE IF EXISTS "public"."gis_safe_risk_dictionary";
CREATE TABLE "public"."gis_safe_risk_dictionary" (
  "id" int4 NOT NULL,
  "name" varchar(255) COLLATE "pg_catalog"."default"
);
COMMENT ON COLUMN "public"."gis_safe_risk_dictionary"."id" IS ' 编号';
COMMENT ON COLUMN "public"."gis_safe_risk_dictionary"."name" IS '风险名称';
COMMENT ON TABLE "public"."gis_safe_risk_dictionary" IS '安全管理-风险类别表';

-- ----------------------------
-- Table structure for gis_safe_risk_level
-- ----------------------------
DROP TABLE IF EXISTS "public"."gis_safe_risk_level";
CREATE TABLE "public"."gis_safe_risk_level" (
  "id" int4 NOT NULL,
  "name" varchar(255) COLLATE "pg_catalog"."default" NOT NULL
);
COMMENT ON COLUMN "public"."gis_safe_risk_level"."id" IS '主键';
COMMENT ON TABLE "public"."gis_safe_risk_level" IS '安全管理-风险级别';

-- ----------------------------
-- Table structure for gis_safety_enum_hidden_type
-- ----------------------------
DROP TABLE IF EXISTS "public"."gis_safety_enum_hidden_type";
CREATE TABLE "public"."gis_safety_enum_hidden_type" (
  "id" serial4 NOT NULL,
  "name" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "type" int2 NOT NULL
);
COMMENT ON COLUMN "public"."gis_safety_enum_hidden_type"."id" IS '自增ID';
COMMENT ON COLUMN "public"."gis_safety_enum_hidden_type"."name" IS '隐患检查类别名称';
COMMENT ON COLUMN "public"."gis_safety_enum_hidden_type"."type" IS '隐患检查类别分类,1.为综合上级检查, 2.为矿内自查';
COMMENT ON TABLE "public"."gis_safety_enum_hidden_type" IS '隐患检查类别字典表';

-- ----------------------------
-- Table structure for gis_safety_enum_risk_type
-- ----------------------------
DROP TABLE IF EXISTS "public"."gis_safety_enum_risk_type";
CREATE TABLE "public"."gis_safety_enum_risk_type" (
  "id" serial4 NOT NULL,
  "risk_type" varchar(255) COLLATE "pg_catalog"."default" NOT NULL
);
COMMENT ON COLUMN "public"."gis_safety_enum_risk_type"."id" IS '自增pk';
COMMENT ON TABLE "public"."gis_safety_enum_risk_type" IS '水文地质类型表';

-- ----------------------------
-- Table structure for gis_safety_forbidden
-- ----------------------------
DROP TABLE IF EXISTS "public"."gis_safety_forbidden";
CREATE TABLE "public"."gis_safety_forbidden" (
  "id" serial4 NOT NULL,
  "gis_mine_information_id" int2,
  "check_org_id" int4 NOT NULL,
  "check_date" timestamp(6) NOT NULL,
  "member" varchar(255) COLLATE "pg_catalog"."default",
  "checker" varchar(255) COLLATE "pg_catalog"."default",
  "location_id" int4 NOT NULL,
  "job" varchar(255) COLLATE "pg_catalog"."default",
  "org_id" int4 NOT NULL,
  "forbidden_type" int2 NOT NULL,
  "content" varchar(255) COLLATE "pg_catalog"."default",
  "rule_id" int4 NOT NULL,
  "result" varchar(255) COLLATE "pg_catalog"."default"
);
COMMENT ON COLUMN "public"."gis_safety_forbidden"."id" IS '自增ID';
COMMENT ON COLUMN "public"."gis_safety_forbidden"."gis_mine_information_id" IS '外键关联煤矿信息表，显示煤矿名称';
COMMENT ON COLUMN "public"."gis_safety_forbidden"."check_org_id" IS '检查单位,从组织机构表关联';
COMMENT ON COLUMN "public"."gis_safety_forbidden"."check_date" IS '检查时间';
COMMENT ON COLUMN "public"."gis_safety_forbidden"."member" IS '违章人员';
COMMENT ON COLUMN "public"."gis_safety_forbidden"."checker" IS '检查人员';
COMMENT ON COLUMN "public"."gis_safety_forbidden"."location_id" IS '检查地点，安全地点表外键';
COMMENT ON COLUMN "public"."gis_safety_forbidden"."job" IS '职务';
COMMENT ON COLUMN "public"."gis_safety_forbidden"."org_id" IS '责任单位，机构组织表外键';
COMMENT ON COLUMN "public"."gis_safety_forbidden"."forbidden_type" IS '三违类型(1.一般，2.严重，3.习惯性)';
COMMENT ON COLUMN "public"."gis_safety_forbidden"."content" IS '三违情况';
COMMENT ON COLUMN "public"."gis_safety_forbidden"."rule_id" IS '处罚依据，外键三违依据表';
COMMENT ON COLUMN "public"."gis_safety_forbidden"."result" IS '处罚结果';
COMMENT ON TABLE "public"."gis_safety_forbidden" IS '三违信息表';

-- ----------------------------
-- Table structure for gis_safety_forbidden_rule
-- ----------------------------
DROP TABLE IF EXISTS "public"."gis_safety_forbidden_rule";
CREATE TABLE "public"."gis_safety_forbidden_rule" (
  "id" serial4 NOT NULL,
  "level" int2 NOT NULL,
  "content" varchar(255) COLLATE "pg_catalog"."default" NOT NULL
);
COMMENT ON COLUMN "public"."gis_safety_forbidden_rule"."id" IS '自增ID';
COMMENT ON COLUMN "public"."gis_safety_forbidden_rule"."level" IS '严重级别(1.一般，2.严重，3.习惯性)';
COMMENT ON TABLE "public"."gis_safety_forbidden_rule" IS '三违制度表';

-- ----------------------------
-- Table structure for gis_safety_hiddentrouble
-- ----------------------------
DROP TABLE IF EXISTS "public"."gis_safety_hiddentrouble";
CREATE TABLE "public"."gis_safety_hiddentrouble" (
  "id" serial4 NOT NULL,
  "gis_mine_information_id" int2,
  "hidden_date" timestamp(6) NOT NULL,
  "type" int2 NOT NULL,
  "classes" int2 NOT NULL,
  "check_org_id" int4 NOT NULL,
  "checker" varchar(255) COLLATE "pg_catalog"."default",
  "follower" varchar(255) COLLATE "pg_catalog"."default",
  "org_id" int4 NOT NULL,
  "assigned_member" varchar(255) COLLATE "pg_catalog"."default",
  "location_id" int4 NOT NULL,
  "hidden_type" int2 NOT NULL,
  "hidden_level" int2 NOT NULL,
  "fixed_date" timestamp(6),
  "review_org_id" int4,
  "content" varchar(255) COLLATE "pg_catalog"."default",
  "suggestion" varchar(255) COLLATE "pg_catalog"."default",
  "hidden_state" int2 NOT NULL,
  "state_content" varchar(255) COLLATE "pg_catalog"."default"
);
COMMENT ON COLUMN "public"."gis_safety_hiddentrouble"."id" IS '自增ID';
COMMENT ON COLUMN "public"."gis_safety_hiddentrouble"."gis_mine_information_id" IS '外键关联煤矿信息表，显示煤矿名称';
COMMENT ON COLUMN "public"."gis_safety_hiddentrouble"."hidden_date" IS '检查时间';
COMMENT ON COLUMN "public"."gis_safety_hiddentrouble"."type" IS '数据类别(1 上级,2. 科室, 3. 区队)';
COMMENT ON COLUMN "public"."gis_safety_hiddentrouble"."classes" IS '检查班次班次：1.早班，2.中班，3. 晚班(具体根据不同的矿区排班制度需要修改这个描述)';
COMMENT ON COLUMN "public"."gis_safety_hiddentrouble"."check_org_id" IS '检查单位,从组织机构表关联';
COMMENT ON COLUMN "public"."gis_safety_hiddentrouble"."checker" IS '检查人员';
COMMENT ON COLUMN "public"."gis_safety_hiddentrouble"."follower" IS '陪同人员';
COMMENT ON COLUMN "public"."gis_safety_hiddentrouble"."org_id" IS '隐患部门(三定单位)，机构组织表外键';
COMMENT ON COLUMN "public"."gis_safety_hiddentrouble"."assigned_member" IS '责任人员';
COMMENT ON COLUMN "public"."gis_safety_hiddentrouble"."location_id" IS '检查地点，安全地点表外键';
COMMENT ON COLUMN "public"."gis_safety_hiddentrouble"."hidden_type" IS '隐患类别(1.采煤，2.掘进，3.一通三防，4.机电，5.安全管理，6.辅助运输，7.地测防治水，8.技术)';
COMMENT ON COLUMN "public"."gis_safety_hiddentrouble"."hidden_level" IS '隐患级别！(1.一般，2.轻微，3.较大，4.重大)';
COMMENT ON COLUMN "public"."gis_safety_hiddentrouble"."fixed_date" IS '限改时间';
COMMENT ON COLUMN "public"."gis_safety_hiddentrouble"."review_org_id" IS '复查单位，机构组织表外键';
COMMENT ON COLUMN "public"."gis_safety_hiddentrouble"."content" IS '隐患内容';
COMMENT ON COLUMN "public"."gis_safety_hiddentrouble"."suggestion" IS '整改意见';
COMMENT ON COLUMN "public"."gis_safety_hiddentrouble"."hidden_state" IS '隐患状态(1.三定中，2.整改中，3.待整改(退回)，4.整改中(延期)，5.待复查，6.复查不通过，7.已销号)';
COMMENT ON COLUMN "public"."gis_safety_hiddentrouble"."state_content" IS '状态内容(不同流转时，留下的流转记录信息)';
COMMENT ON TABLE "public"."gis_safety_hiddentrouble" IS '隐患信息表';

-- ----------------------------
-- Table structure for gis_safety_location_point
-- ----------------------------
DROP TABLE IF EXISTS "public"."gis_safety_location_point";
CREATE TABLE "public"."gis_safety_location_point" (
  "id" serial4 NOT NULL,
  "gis_mine_information_id" int2,
  "name" varchar(255) COLLATE "pg_catalog"."default",
  "marker" jsonb,
  "geo_marker" "public"."geometry",
  "line" jsonb,
  "geo_line" "public"."geometry",
  "entity_id" int4,
  "localtion_type" int4
);
COMMENT ON COLUMN "public"."gis_safety_location_point"."id" IS '自增ID';
COMMENT ON COLUMN "public"."gis_safety_location_point"."gis_mine_information_id" IS '外键关联煤矿信息表，显示煤矿名称';
COMMENT ON COLUMN "public"."gis_safety_location_point"."name" IS '位置名称';
COMMENT ON COLUMN "public"."gis_safety_location_point"."marker" IS '点坐标,jsonb格式';
COMMENT ON COLUMN "public"."gis_safety_location_point"."geo_marker" IS '点坐标，geo格式';
COMMENT ON COLUMN "public"."gis_safety_location_point"."line" IS '线坐标，jsonb格式，用于关联设备，进行计算绑点';
COMMENT ON COLUMN "public"."gis_safety_location_point"."geo_line" IS '线坐标，geo格式，用于关联设备，进行计算绑点';
COMMENT ON COLUMN "public"."gis_safety_location_point"."entity_id" IS '实体分类对应的实体ID,用于关联业务层，目前出现从工作面找安全三违数据，断层情况。';
COMMENT ON COLUMN "public"."gis_safety_location_point"."localtion_type" IS '1、井上2、井下';
COMMENT ON TABLE "public"."gis_safety_location_point" IS '安全地点信息表(用于gis展示机电设备业务和三违，隐患，风险业务的关联绑定)';

-- ----------------------------
-- Table structure for gis_safety_risk_analysis
-- ----------------------------
DROP TABLE IF EXISTS "public"."gis_safety_risk_analysis";
CREATE TABLE "public"."gis_safety_risk_analysis" (
  "id" serial4 NOT NULL,
  "gis_mine_information_id" int2,
  "name" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "type" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "location_id" int4 NOT NULL,
  "admin" varchar(255) COLLATE "pg_catalog"."default",
  "members" varchar(255) COLLATE "pg_catalog"."default",
  "conclusion" varchar(255) COLLATE "pg_catalog"."default",
  "risk_date" timestamp(6) NOT NULL
);
COMMENT ON COLUMN "public"."gis_safety_risk_analysis"."id" IS '自增ID';
COMMENT ON COLUMN "public"."gis_safety_risk_analysis"."gis_mine_information_id" IS '外键关联煤矿信息表，显示煤矿名称';
COMMENT ON COLUMN "public"."gis_safety_risk_analysis"."name" IS '辨识名称';
COMMENT ON COLUMN "public"."gis_safety_risk_analysis"."type" IS '辨识类型';
COMMENT ON COLUMN "public"."gis_safety_risk_analysis"."location_id" IS '外键关联风险区域表';
COMMENT ON COLUMN "public"."gis_safety_risk_analysis"."admin" IS '负责人';
COMMENT ON COLUMN "public"."gis_safety_risk_analysis"."members" IS '参加人';
COMMENT ON COLUMN "public"."gis_safety_risk_analysis"."conclusion" IS '结论';
COMMENT ON COLUMN "public"."gis_safety_risk_analysis"."risk_date" IS '辨识时间';
COMMENT ON TABLE "public"."gis_safety_risk_analysis" IS '风险辨识评估表';

-- ----------------------------
-- Table structure for gis_safety_risk_location
-- ----------------------------
DROP TABLE IF EXISTS "public"."gis_safety_risk_location";
CREATE TABLE "public"."gis_safety_risk_location" (
  "id" serial4 NOT NULL,
  "location_id" int4 NOT NULL,
  "risk_id" int4 NOT NULL,
  "relation_date" timestamp(6) NOT NULL
);
COMMENT ON COLUMN "public"."gis_safety_risk_location"."id" IS '自增ID';
COMMENT ON COLUMN "public"."gis_safety_risk_location"."location_id" IS '风险地点表外键';
COMMENT ON COLUMN "public"."gis_safety_risk_location"."risk_id" IS '风险信息表外键';
COMMENT ON COLUMN "public"."gis_safety_risk_location"."relation_date" IS '关联时刻时间';
COMMENT ON TABLE "public"."gis_safety_risk_location" IS '风险区域管理信息表';

-- ----------------------------
-- Table structure for gis_safety_risk_record
-- ----------------------------
DROP TABLE IF EXISTS "public"."gis_safety_risk_record";
CREATE TABLE "public"."gis_safety_risk_record" (
  "id" serial4 NOT NULL,
  "gis_mine_information_id" int2,
  "location" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "risk_type_id" int4 NOT NULL,
  "org_name" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "admin" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "lead" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "level_id" int4 NOT NULL,
  "risk_date" timestamp(6) NOT NULL,
  "description" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "action" varchar(255) COLLATE "pg_catalog"."default" NOT NULL
);
COMMENT ON COLUMN "public"."gis_safety_risk_record"."id" IS '自增ID';
COMMENT ON COLUMN "public"."gis_safety_risk_record"."gis_mine_information_id" IS '外键关联煤矿信息表，显示煤矿名称';
COMMENT ON COLUMN "public"."gis_safety_risk_record"."location" IS '管控措施';
COMMENT ON COLUMN "public"."gis_safety_risk_record"."risk_type_id" IS '风险类别表外键';
COMMENT ON COLUMN "public"."gis_safety_risk_record"."org_name" IS '责任单位';
COMMENT ON COLUMN "public"."gis_safety_risk_record"."admin" IS '责任人';
COMMENT ON COLUMN "public"."gis_safety_risk_record"."lead" IS '分管矿领导';
COMMENT ON COLUMN "public"."gis_safety_risk_record"."level_id" IS '风险等级：1. 低风险，2. 一般风险，3.较大风险，4.重大风险';
COMMENT ON COLUMN "public"."gis_safety_risk_record"."risk_date" IS '辨识时间';
COMMENT ON COLUMN "public"."gis_safety_risk_record"."description" IS '风险及其后果描述';
COMMENT ON TABLE "public"."gis_safety_risk_record" IS '风险信息记录表';

-- ----------------------------
-- Table structure for gis_subsystem
-- ----------------------------
DROP TABLE IF EXISTS "public"."gis_subsystem";
CREATE TABLE "public"."gis_subsystem" (
  "id" serial4 NOT NULL,
  "name" varchar(255) COLLATE "pg_catalog"."default",
  "icon" jsonb
);
COMMENT ON COLUMN "public"."gis_subsystem"."name" IS '数据源名称';
COMMENT ON COLUMN "public"."gis_subsystem"."icon" IS '图标';
COMMENT ON TABLE "public"."gis_subsystem" IS '子系统字典表';

-- ----------------------------
-- Table structure for gis_surface_subsys_ref
-- ----------------------------
DROP TABLE IF EXISTS "public"."gis_surface_subsys_ref";
CREATE TABLE "public"."gis_surface_subsys_ref" (
  "id" serial4 NOT NULL,
  "surface_id" int4,
  "subsys_id" varchar(255) COLLATE "pg_catalog"."default"
);
COMMENT ON COLUMN "public"."gis_surface_subsys_ref"."surface_id" IS '工作面id';
COMMENT ON COLUMN "public"."gis_surface_subsys_ref"."subsys_id" IS '子系统id';
COMMENT ON TABLE "public"."gis_surface_subsys_ref" IS '工作面_子系统_关联表';

-- ----------------------------
-- Table structure for viewport
-- ----------------------------
DROP TABLE IF EXISTS "public"."viewport";
CREATE TABLE "public"."viewport" (
  "id" serial4 NOT NULL,
  "name" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "meta" jsonb NOT NULL,
  "snapshot" jsonb
);
COMMENT ON COLUMN "public"."viewport"."id" IS '自增ID';
COMMENT ON COLUMN "public"."viewport"."name" IS '视口名称';
COMMENT ON COLUMN "public"."viewport"."meta" IS '视口源信息';
COMMENT ON COLUMN "public"."viewport"."snapshot" IS '快照信息';
COMMENT ON TABLE "public"."viewport" IS '三维视口';

-- public.database_version_control_seq definition
-- DROP SEQUENCE public.database_version_control_seq;

-- ----------------------------
-- Table structure for database_version_control
-- ----------------------------
DROP TABLE IF EXISTS "public"."database_version_control";
CREATE TABLE "public"."database_version_control" (
  "id" serial4 NOT NULL,
  "name" varchar(20) COLLATE "pg_catalog"."default",
  "version" int8,
  "up_sql" text COLLATE "pg_catalog"."default",
  "down_sql" text COLLATE "pg_catalog"."default",
  "reference_items" varchar(100) COLLATE "pg_catalog"."default",
  "create_time" timestamp(6),
  "message" varchar(50) COLLATE "pg_catalog"."default"
);
COMMENT ON COLUMN "public"."database_version_control"."id" IS '记录ID';
COMMENT ON COLUMN "public"."database_version_control"."name" IS '名称';
COMMENT ON COLUMN "public"."database_version_control"."version" IS '版本号';
COMMENT ON COLUMN "public"."database_version_control"."up_sql" IS '本次版本的升级sql';
COMMENT ON COLUMN "public"."database_version_control"."down_sql" IS '本次版本的降级sql';
COMMENT ON COLUMN "public"."database_version_control"."reference_items" IS '参考项目';
COMMENT ON COLUMN "public"."database_version_control"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."database_version_control"."message" IS '备注';
COMMENT ON TABLE "public"."database_version_control" IS '数据库版本控制表';

-- ----------------------------
-- Table structure for databasechangelog
-- ----------------------------
DROP TABLE IF EXISTS "public"."databasechangelog";
CREATE TABLE "public"."databasechangelog" (
  "id" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "author" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "filename" varchar(255) COLLATE "pg_catalog"."default",
  "dateexecuted" timestamp(6) NOT NULL,
  "orderexecuted" int4 NOT NULL,
  "exectype" varchar(10) COLLATE "pg_catalog"."default",
  "md5sum" varchar(35) COLLATE "pg_catalog"."default",
  "description" varchar(255) COLLATE "pg_catalog"."default",
  "comments" varchar(255) COLLATE "pg_catalog"."default",
  "tag" varchar(255) COLLATE "pg_catalog"."default",
  "liquibase" varchar(20) COLLATE "pg_catalog"."default",
  "contexts" varchar(255) COLLATE "pg_catalog"."default",
  "labels" varchar(255) COLLATE "pg_catalog"."default",
  "deployment_id" varchar(10) COLLATE "pg_catalog"."default"
);

-- ----------------------------
-- Table structure for databasechangeloglock
-- ----------------------------
DROP TABLE IF EXISTS "public"."databasechangeloglock";
CREATE TABLE "public"."databasechangeloglock" (
  "id" int4 NOT NULL,
  "locked" bool NOT NULL,
  "lockgranted" timestamp(6),
  "lockedby" varchar(255) COLLATE "pg_catalog"."default"
);

-- ----------------------------
-- Indexes structure for table viewport
-- ----------------------------
CREATE UNIQUE INDEX "ix_viewport_name" ON "public"."viewport" USING btree (
  "name" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table app_login
-- ----------------------------
ALTER TABLE "public"."app_login" ADD CONSTRAINT "app_login_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table bdata_alert
-- ----------------------------
ALTER TABLE "public"."bdata_alert" ADD CONSTRAINT "pk_bdata_alert" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table bdata_alert_indictor
-- ----------------------------
ALTER TABLE "public"."bdata_alert_indictor" ADD CONSTRAINT "pk_bdata_alert_indictor" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table bdata_alert_indictor_type
-- ----------------------------
ALTER TABLE "public"."bdata_alert_indictor_type" ADD CONSTRAINT "pk_bdata_alert_indictor_type" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table bdata_defect_db
-- ----------------------------
ALTER TABLE "public"."bdata_defect_db" ADD CONSTRAINT "pk_bdata_defect_db" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table bdata_diag_indictor
-- ----------------------------
ALTER TABLE "public"."bdata_diag_indictor" ADD CONSTRAINT "pk_bdata_diag_indictor" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table bdata_diag_result
-- ----------------------------
ALTER TABLE "public"."bdata_diag_result" ADD CONSTRAINT "pk_bdata_diag_result" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table bdata_oper_regulation
-- ----------------------------
ALTER TABLE "public"."bdata_oper_regulation" ADD CONSTRAINT "pk_bdata_oper_regulation" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table bdata_safety_regulation
-- ----------------------------
ALTER TABLE "public"."bdata_safety_regulation" ADD CONSTRAINT "pk_bdata_safety_regulation" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table bdata_triviolation_db
-- ----------------------------
ALTER TABLE "public"."bdata_triviolation_db" ADD CONSTRAINT "pk_bdata_triviolation_db" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table database_version_control
-- ----------------------------
ALTER TABLE "public"."database_version_control" ADD CONSTRAINT "database_version_control_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table databasechangeloglock
-- ----------------------------
ALTER TABLE "public"."databasechangeloglock" ADD CONSTRAINT "databasechangeloglock_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table emergency_accident_configure
-- ----------------------------
ALTER TABLE "public"."emergency_accident_configure" ADD CONSTRAINT "emergency_accident_configure_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table emergency_accident_img
-- ----------------------------
ALTER TABLE "public"."emergency_accident_img" ADD CONSTRAINT "emergency_accident_img_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table emergency_configure_mapping
-- ----------------------------
ALTER TABLE "public"."emergency_configure_mapping" ADD CONSTRAINT "emergency_configure_mapping_pkey" PRIMARY KEY ("id", "accident_level");

-- ----------------------------
-- Primary Key structure for table gis_accident_file
-- ----------------------------
ALTER TABLE "public"."gis_accident_file" ADD CONSTRAINT "gis_accident_file_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table gis_bind_build_point
-- ----------------------------
ALTER TABLE "public"."gis_bind_build_point" ADD CONSTRAINT "gis_bind_build_point_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table gis_bind_build_point_sub
-- ----------------------------
ALTER TABLE "public"."gis_bind_build_point_sub" ADD CONSTRAINT "gis_bind_buid_point_sub_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table gis_bind_point
-- ----------------------------
ALTER TABLE "public"."gis_bind_point" ADD CONSTRAINT "gis_bind_point_pkey" PRIMARY KEY ("id", "gis_type_subsystem");

-- ----------------------------
-- Primary Key structure for table gis_bind_point_sub
-- ----------------------------
ALTER TABLE "public"."gis_bind_point_sub" ADD CONSTRAINT "gis_bind_point_sub_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table gis_entity_class
-- ----------------------------
ALTER TABLE "public"."gis_entity_class" ADD CONSTRAINT "gis_entity_class_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table gis_heading_day_record
-- ----------------------------
ALTER TABLE "public"."gis_heading_day_record" ADD CONSTRAINT "gis_heading_day_record_pkey" PRIMARY KEY ("heading_time", "heading_id");

-- ----------------------------
-- Primary Key structure for table gis_mainpage
-- ----------------------------
ALTER TABLE "public"."gis_mainpage" ADD CONSTRAINT "gis_mainpage_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table gis_map_enum
-- ----------------------------
ALTER TABLE "public"."gis_map_enum" ADD CONSTRAINT "gis_map_enum_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table gis_map_info
-- ----------------------------
ALTER TABLE "public"."gis_map_info" ADD CONSTRAINT "gis_map_info_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table gis_map_info_content_category
-- ----------------------------
ALTER TABLE "public"."gis_map_info_content_category" ADD CONSTRAINT "gis_map_info_content_category_pkey" PRIMARY KEY ("system_type", "system_id", "name");

-- ----------------------------
-- Primary Key structure for table gis_map_info_mapping_map_layer
-- ----------------------------
ALTER TABLE "public"."gis_map_info_mapping_map_layer" ADD CONSTRAINT "gis_map_info_mapping_map_layer_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table gis_map_subbiz
-- ----------------------------
ALTER TABLE "public"."gis_map_subbiz" ADD CONSTRAINT "gis_map_subbiz_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table gis_map_subbiz_conf
-- ----------------------------
ALTER TABLE "public"."gis_map_subbiz_conf" ADD CONSTRAINT "gis_map_subbiz_conf_pkey" PRIMARY KEY ("map_id", "subbiz_id");

-- ----------------------------
-- Primary Key structure for table gis_mining_day_record
-- ----------------------------
ALTER TABLE "public"."gis_mining_day_record" ADD CONSTRAINT "gis_mining_day_record_pkey" PRIMARY KEY ("mining_time", "mining_id");

-- ----------------------------
-- Primary Key structure for table gis_position_entity
-- ----------------------------
ALTER TABLE "public"."gis_position_entity" ADD CONSTRAINT "gis_position_entity_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table gis_public_config
-- ----------------------------
ALTER TABLE "public"."gis_public_config" ADD CONSTRAINT "gis_public_config_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table gis_rename_bind
-- ----------------------------
ALTER TABLE "public"."gis_rename_bind" ADD CONSTRAINT "gis_rename_bind_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table gis_safe_risk_dictionary
-- ----------------------------
ALTER TABLE "public"."gis_safe_risk_dictionary" ADD CONSTRAINT "gis_safe_risk_dictionary_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table gis_safe_risk_level
-- ----------------------------
ALTER TABLE "public"."gis_safe_risk_level" ADD CONSTRAINT "gis_safe_risk_level_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table gis_safety_enum_hidden_type
-- ----------------------------
ALTER TABLE "public"."gis_safety_enum_hidden_type" ADD CONSTRAINT "gis_safety_enum_hidden_type_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table gis_safety_forbidden
-- ----------------------------
ALTER TABLE "public"."gis_safety_forbidden" ADD CONSTRAINT "gis_safety_forbidden_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table gis_safety_forbidden_rule
-- ----------------------------
ALTER TABLE "public"."gis_safety_forbidden_rule" ADD CONSTRAINT "gis_safety_forbidden_rule_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table gis_safety_hiddentrouble
-- ----------------------------
ALTER TABLE "public"."gis_safety_hiddentrouble" ADD CONSTRAINT "gis_safety_hiddentrouble_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table gis_safety_location_point
-- ----------------------------
ALTER TABLE "public"."gis_safety_location_point" ADD CONSTRAINT "gis_safety_location_point_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table gis_safety_risk_analysis
-- ----------------------------
ALTER TABLE "public"."gis_safety_risk_analysis" ADD CONSTRAINT "gis_safety_risk_analysis_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table gis_safety_risk_location
-- ----------------------------
ALTER TABLE "public"."gis_safety_risk_location" ADD CONSTRAINT "gis_safety_risk_location_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table gis_safety_risk_record
-- ----------------------------
ALTER TABLE "public"."gis_safety_risk_record" ADD CONSTRAINT "gis_safety_risk_record_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table gis_subsystem
-- ----------------------------
ALTER TABLE "public"."gis_subsystem" ADD CONSTRAINT "gis_subsystem_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table gis_surface_subsys_ref
-- ----------------------------
ALTER TABLE "public"."gis_surface_subsys_ref" ADD CONSTRAINT "pk_gis_surface_subsys_ref" PRIMARY KEY ("id");

---流程图配置
-- ----------------------------
-- Table structure for emergency_accident_configure
-- ----------------------------
DROP TABLE IF EXISTS "emergency_accident_configure";
CREATE TABLE "emergency_accident_configure" (
  "id" serial4 NOT NULL,
  "configure" text COLLATE "pg_catalog"."default",
  "name" varchar(255) COLLATE "pg_catalog"."default",
  "explain" varchar(255) COLLATE "pg_catalog"."default",
  "acc_id" varchar(32) COLLATE "pg_catalog"."default",
  "is_delete" bool
);
COMMENT ON COLUMN "emergency_accident_configure"."configure" IS '流程图配置信息';
COMMENT ON COLUMN "emergency_accident_configure"."name" IS '流程图名称';
COMMENT ON COLUMN "emergency_accident_configure"."explain" IS '流程图说明';
COMMENT ON COLUMN "emergency_accident_configure"."acc_id" IS '类型关联id';
COMMENT ON TABLE "emergency_accident_configure" IS '流程图配置信息表';

-- ----------------------------
-- Table structure for emergency_accident_img
-- ----------------------------
DROP TABLE IF EXISTS "emergency_accident_img";
CREATE TABLE "emergency_accident_img" (
  "id" serial4 NOT NULL,
  "img_name" varchar(255) COLLATE "pg_catalog"."default",
  "img_url" varchar COLLATE "pg_catalog"."default",
  "url" varchar COLLATE "pg_catalog"."default"
);
COMMENT ON COLUMN "emergency_accident_img"."img_name" IS '图标名称';
COMMENT ON COLUMN "emergency_accident_img"."img_url" IS '图标地址';
COMMENT ON COLUMN "emergency_accident_img"."url" IS '图标地址';
COMMENT ON TABLE "emergency_accident_img" IS '流程图图标库 表';

-- ----------------------------
-- Table structure for emergency_configure_mapping
-- ----------------------------
DROP TABLE IF EXISTS "emergency_configure_mapping";
CREATE TABLE "emergency_configure_mapping" (
  "id" serial4 NOT NULL,
  "accident_type" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "accident_level" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "created_at" timestamptz(6) DEFAULT now(),
  "update_at" timestamptz(6) DEFAULT now(),
  "mapping_id" int4
);
COMMENT ON COLUMN "emergency_configure_mapping"."id" IS '事故类型id';
COMMENT ON COLUMN "emergency_configure_mapping"."accident_type" IS '事故类型';
COMMENT ON COLUMN "emergency_configure_mapping"."accident_level" IS '事故级别';
COMMENT ON COLUMN "emergency_configure_mapping"."created_at" IS '创建时间';
COMMENT ON COLUMN "emergency_configure_mapping"."update_at" IS '修改时间';
COMMENT ON COLUMN "emergency_configure_mapping"."mapping_id" IS '流程图id';

-- ----------------------------
-- Primary Key structure for table emergency_accident_configure
-- ----------------------------
ALTER TABLE "emergency_accident_configure" ADD CONSTRAINT "emergency_accident_configure_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table emergency_accident_img
-- ----------------------------
ALTER TABLE "emergency_accident_img" ADD CONSTRAINT "emergency_accident_img_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table emergency_configure_mapping
-- ----------------------------
ALTER TABLE "emergency_configure_mapping" ADD CONSTRAINT "emergency_configure_mapping_pkey" PRIMARY KEY ("id", "accident_level");

-- 避灾路线配置
DROP TABLE IF EXISTS "public"."position_employee_setting";
CREATE TABLE "public"."position_employee_setting" (
  "id" serial4 NOT NULL,
  "start_point_id" text COLLATE "pg_catalog"."default",
  "end_point_id" text COLLATE "pg_catalog"."default",
  "create_time" varchar(255) COLLATE "pg_catalog"."default",
  "update_time" varchar(255) COLLATE "pg_catalog"."default",
  "color" varchar(255) COLLATE "pg_catalog"."default",
  "line" text COLLATE "pg_catalog"."default",
  "start_name" varchar(255) COLLATE "pg_catalog"."default",
  "start_latlng" text COLLATE "pg_catalog"."default",
  "end_name" varchar(255) COLLATE "pg_catalog"."default",
  "end_latlng" text COLLATE "pg_catalog"."default",
  CONSTRAINT "position_employee_setting_pkey" PRIMARY KEY ("id")
);
ALTER TABLE "public"."position_employee_setting" OWNER TO "postgres";
COMMENT ON COLUMN "public"."position_employee_setting"."id" IS '主键自增';
COMMENT ON COLUMN "public"."position_employee_setting"."start_point_id" IS '起始基站';
COMMENT ON COLUMN "public"."position_employee_setting"."end_point_id" IS '结束基站';
COMMENT ON COLUMN "public"."position_employee_setting"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."position_employee_setting"."update_time" IS '更新时间';
COMMENT ON COLUMN "public"."position_employee_setting"."color" IS '线路颜色';
COMMENT ON COLUMN "public"."position_employee_setting"."line" IS '线数据';

-- 机电设备划线布点
-- ----------------------------
-- Sequence structure for gis_line_bind_point_id_seq
-- ----------------------------
-- ----------------------------
-- Table structure for gis_line_bind_point
-- ----------------------------
DROP TABLE IF EXISTS "gis_line_bind_point";
CREATE TABLE "gis_line_bind_point" (
  "id" serial4 NOT NULL,
  "name" varchar(255) COLLATE "pg_catalog"."default",
  "colour" varchar(255) COLLATE "pg_catalog"."default",
  "coordinate" jsonb,
  "center_point" jsonb,
  "icon" jsonb,
  "marker_id" int4,
  "polygon_id" int4,
  "line_id" int4,
  "topo_id" varchar(32) COLLATE "pg_catalog"."default",
  "auto_id" varchar(32) COLLATE "pg_catalog"."default",
  "point_id" varchar(255) COLLATE "pg_catalog"."default",
  "geometry_type" int2 NOT NULL
);
COMMENT ON COLUMN "gis_line_bind_point"."id" IS '主键id';
COMMENT ON COLUMN "gis_line_bind_point"."name" IS '名称';
COMMENT ON COLUMN "gis_line_bind_point"."colour" IS '颜色';
COMMENT ON COLUMN "gis_line_bind_point"."coordinate" IS '坐标';
COMMENT ON COLUMN "gis_line_bind_point"."center_point" IS '中心点';
COMMENT ON COLUMN "gis_line_bind_point"."icon" IS '图标';
COMMENT ON COLUMN "gis_line_bind_point"."marker_id" IS '关联传感器';
COMMENT ON COLUMN "gis_line_bind_point"."polygon_id" IS '关联工作面';
COMMENT ON COLUMN "gis_line_bind_point"."line_id" IS '关联巷道';
COMMENT ON COLUMN "gis_line_bind_point"."topo_id" IS '关联topo图';
COMMENT ON COLUMN "gis_line_bind_point"."auto_id" IS '关联自动化';
COMMENT ON COLUMN "gis_line_bind_point"."point_id" IS '关联绑点id';
COMMENT ON COLUMN "gis_line_bind_point"."geometry_type" IS '1点 2 线 3 面';
COMMENT ON TABLE "gis_line_bind_point" IS '划线布点表';

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
-- ----------------------------
-- Primary Key structure for table gis_line_bind_point
-- ----------------------------
ALTER TABLE "gis_line_bind_point" ADD CONSTRAINT "gis_line_bind_point_pkey" PRIMARY KEY ("id");

-- 增加自动进尺开关配置
ALTER TABLE "public"."gis_bind_build_point" ADD COLUMN "move_type" int2 DEFAULT 0;
COMMENT ON COLUMN "public"."gis_bind_build_point"."move_type" IS '工作面自动进尺开关,1打开,0关闭';

-- 摄像机别名
CREATE TABLE "public"."camera_display" (
  "id" int4 NOT NULL,
  "display_name" VARCHAR (255) COLLATE "pg_catalog"."default",
  CONSTRAINT "camera_display_pkey" PRIMARY KEY ("id")
);
ALTER TABLE "public"."camera_display" OWNER TO "postgres";
COMMENT ON COLUMN "public"."camera_display"."display_name" IS '摄像机别名';
COMMENT ON TABLE "public"."camera_display" IS '摄像机别名表';

--三维模型树配置
-- public.gis_three_conf definition
-- Drop table
-- DROP TABLE public.gis_three_conf;
CREATE TABLE public.gis_three_conf (
  id serial4 NOT NULL,
  "name" varchar(64) NOT NULL, -- 名字
  color varchar(64) NULL, -- 颜色
  frame_color varchar(64) NULL, -- 边框颜色
  frame_wide float8 NULL, -- 边框宽度
  transparent float8 NULL, -- 透明度
  shadow bool NULL DEFAULT false, -- 是否需要阴影（默认false）
  category varchar NULL, -- 分类，HD和QY
  name_hidden bool NULL DEFAULT true, -- 名字是否隐藏，默认true 隐藏 ；false 显示
  CONSTRAINT gis_three_conf_pkey PRIMARY KEY (id)
);
COMMENT ON TABLE public.gis_three_conf IS '三维配置表';
COMMENT ON COLUMN public.gis_three_conf."name" IS '名字';
COMMENT ON COLUMN public.gis_three_conf.color IS '颜色';
COMMENT ON COLUMN public.gis_three_conf.frame_color IS '边框颜色';
COMMENT ON COLUMN public.gis_three_conf.frame_wide IS '边框宽度';
COMMENT ON COLUMN public.gis_three_conf.transparent IS '透明度';
COMMENT ON COLUMN public.gis_three_conf.shadow IS '是否需要阴影（默认false）';
COMMENT ON COLUMN public.gis_three_conf.category IS '分类，HD和QY';
COMMENT ON COLUMN public.gis_three_conf.name_hidden IS '名字是否隐藏，默认true 隐藏 ；false 显示';

-- public.gis_three_model_category definition
-- Drop table
-- DROP TABLE public.gis_three_model_category;
CREATE TABLE public.gis_three_model_category (
  id varchar NOT NULL,
  "name" varchar NOT NULL,
  insert_time timestamp NULL DEFAULT now(), -- 入库时间
  conf_id int4 NULL,
  CONSTRAINT gis_three_model_category_pk PRIMARY KEY (id)
);
COMMENT ON TABLE public.gis_three_model_category IS '三维模型分类';
COMMENT ON COLUMN public.gis_three_model_category.insert_time IS '入库时间';

ALTER TABLE public.gis_three_conf ADD label_flag bool NULL DEFAULT false;
COMMENT ON COLUMN public.gis_three_conf.label_flag IS '是否是标签';
ALTER TABLE public.gis_three_conf ADD address varchar NULL;
COMMENT ON COLUMN public.gis_three_conf.address IS '名称位置';
ALTER TABLE public.gis_three_conf ADD icon varchar NULL;
COMMENT ON COLUMN public.gis_three_conf.icon IS '图标';
ALTER TABLE public.gis_three_conf ADD icon_display bool NULL DEFAULT false;
COMMENT ON COLUMN public.gis_three_conf.icon_display IS '是否展示图标,true 展示;false 隐藏';
ALTER TABLE public.gis_three_conf ADD legend varchar NULL;
COMMENT ON COLUMN public.gis_three_conf.legend IS '图例';
ALTER TABLE public.gis_three_conf ADD legend_display bool NULL DEFAULT false;
COMMENT ON COLUMN public.gis_three_conf.legend_display IS '图例是否展示,true 展示;false 隐藏';
ALTER TABLE public.gis_three_conf ADD shine_display bool NULL DEFAULT false;
COMMENT ON COLUMN public.gis_three_conf.shine_display IS '是否发光,true 展示;false 隐藏';
ALTER TABLE public.gis_mainpage add ratio integer;
COMMENT ON COLUMN public.gis_mainpage.ratio is '放大比例';
ALTER TABLE public.gis_map_info add ratio integer;
COMMENT ON COLUMN public.gis_map_info.ratio is '比例';
ALTER TABLE public.gis_map_info add orientation integer;
COMMENT ON COLUMN public.gis_map_info.orientation is '旋转视角';

-- public.gis_three_cutline definition
-- Drop table
-- DROP TABLE public.gis_three_cutline;
CREATE TABLE public.gis_three_cutline (
  id serial4 NOT NULL,
  name varchar(255) NULL, -- 名称
  icon_map varchar(255) NULL, -- 三维图标
  icon_info varchar(255) NULL -- 菜单图标
);
COMMENT ON TABLE public.gis_three_cutline IS '三维图例';
COMMENT ON COLUMN public.gis_three_cutline.name IS '名称';
COMMENT ON COLUMN public.gis_three_cutline.icon_map IS '三维图标';
COMMENT ON COLUMN public.gis_three_cutline.icon_info IS '菜单图标';

-- public.gis_three_menu definition
-- Drop table
-- DROP TABLE public.gis_three_menu;
CREATE TABLE public.gis_three_menu (
  id serial4 NOT NULL,
  layout varchar(255) NULL, -- 前端自己维护的map
  "name" varchar(255) NULL, -- 名称
  rotating_list jsonb NULL, -- 视角旋转的列表
  sort int4 NULL, -- 排序字段
  three_geometry_coordinate jsonb NULL, -- 三维坐标
  icon_url varchar(255) NULL, -- 图标
  pid int4 NULL, -- 父类Id
  "level" int4 NULL -- 层级
);
COMMENT ON COLUMN public.gis_three_menu.layout IS '前端自己维护的map';
COMMENT ON COLUMN public.gis_three_menu."name" IS '名称';
COMMENT ON COLUMN public.gis_three_menu.rotating_list IS '视角旋转的列表';
COMMENT ON COLUMN public.gis_three_menu.sort IS '排序字段';
COMMENT ON COLUMN public.gis_three_menu.three_geometry_coordinate IS '三维坐标';
COMMENT ON COLUMN public.gis_three_menu.icon_url IS '图标';
COMMENT ON COLUMN public.gis_three_menu.pid IS '父类Id';
COMMENT ON COLUMN public.gis_three_menu."level" IS '层级';

ALTER TABLE public.gis_three_conf ADD display_name varchar NULL;
COMMENT ON COLUMN public.gis_three_conf.display_name IS '别名';
ALTER TABLE public.gis_three_cutline
ALTER COLUMN icon_map type jsonb using icon_map::jsonb;
ALTER TABLE public.gis_three_cutline alter column icon_info type jsonb using icon_info::jsonb;

-- 三维模型配置增加字段
ALTER TABLE public.gis_three_model_category ADD topo_id varchar NULL;
COMMENT ON COLUMN public.gis_three_model_category.topo_id IS 'topoId';
ALTER TABLE public.gis_three_model_category ADD coordinate jsonb NULL;
COMMENT ON COLUMN public.gis_three_model_category.coordinate IS '坐标';
ALTER TABLE public.gis_three_model_category ADD display_name varchar NULL;
COMMENT ON COLUMN public.gis_three_model_category.display_name IS '别名';

-- 增加分类的旋转角度
ALTER TABLE public.gis_three_model_category ADD three_rotation jsonb NULL;
COMMENT ON COLUMN public.gis_three_model_category.three_rotation IS '旋转角度';

-- 绑点功能增加三维模型配置和巷道配置
ALTER TABLE public.gis_bind_point ADD category varchar NULL;
COMMENT ON COLUMN public.gis_bind_point.category IS '三维模型id的类型';
ALTER TABLE  public.gis_bind_point ADD model_id varchar NULL;
COMMENT ON COLUMN public.gis_bind_point.model_id IS '三维模型id';
ALTER TABLE public.gis_bind_point ADD real_tunnel_name varchar NULL;
COMMENT ON COLUMN public.gis_bind_point.real_tunnel_name IS '真实巷道名字';

-- 三维配置增加不可删除字段
ALTER TABLE public.gis_three_conf ADD no_delete bool NULL DEFAULT false;
COMMENT ON COLUMN public.gis_three_conf.no_delete IS '不可移除';
ALTER TABLE public.gis_line_bind_point
ALTER COLUMN point_id type text;

-- GIS 增加旋转角度和放大比例的字段
ALTER TABLE public.gis_map_info ADD ratio int4 NULL;
COMMENT ON COLUMN public.gis_map_info.ratio IS '比例';
ALTER TABLE public.gis_map_info ADD orientation int4 NULL;
COMMENT ON COLUMN public.gis_map_info.orientation IS '旋转视角';
ALTER TABLE public.gis_mainpage ADD ratio int4 NULL;
COMMENT ON COLUMN public.gis_mainpage.ratio IS '放大比例';

-- 每日进尺数据存储格式修改
ALTER TABLE public.gis_heading_day_record ALTER COLUMN heading_upper_chute TYPE numeric USING heading_upper_chute::numeric;
ALTER TABLE public.gis_mining_day_record ALTER COLUMN mining_upper_chute TYPE numeric USING mining_upper_chute::numeric;
ALTER TABLE public.gis_mining_day_record ALTER COLUMN mining_lower_chute TYPE numeric USING mining_lower_chute::numeric;
