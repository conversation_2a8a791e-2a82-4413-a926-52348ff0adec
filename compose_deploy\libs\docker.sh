#!/bin/bash

#===============================================================================
# Docker 操作工具库
#===============================================================================
# 功能描述: 提供 Docker 网络、数据卷、镜像和容器编排管理功能
# 依赖模块: utils.sh (echo_error, echo_yellow, echo_ok 函数)
#           config/.config.env (DOCKER_NETWORKS, DOCKER_VOLUMES 等配置)
# 函数列表：
# - docker_compose_cmd
# - create_docker_network *
# - remove_docker_network *
# - create_docker_volume *
# - remove_docker_volume *
# - remove_docker_volumes_safely
# - backup_docker_volume
# - restore_docker_volume *
# - pull_docker_image *
# - docker_compose_pull_with_retry
# - docker_compose_up
# - docker_compose_down
# - docker_compose_start
# - docker_compose_stop
# - docker_compose_restart
# - init_docker
# - prune_docker
#===============================================================================

#-------------------------------------------------------------------------------
# 函数名: docker_compose_cmd
# 功能: 使用 Docker Compose 命令
# 参数: $@ - Docker Compose 命令参数
# 返回: 执行 Docker Compose 命令
#-------------------------------------------------------------------------------
docker_compose_cmd() {
  # 只使用 docker compose v2
  if docker compose version >/dev/null 2>&1; then
    docker compose "$@"
  else
    echo_error "未找到可用的 Docker Compose 命令"
    echo_error "请确保已安装 Docker Compose (docker compose)"
    return 1
  fi
}

#-------------------------------------------------------------------------------
# 函数名: create_docker_network
# 功能: 创建 Docker 网络（如果不存在）
# 参数: $1 - 网络名称
# 返回: 无
#-------------------------------------------------------------------------------
create_docker_network() {
  local net_name="$1"
  local net_flag

  # 检查网络是否已存在
  net_flag=$(docker network ls | grep -w "$net_name" | wc -l)
  if [ $net_flag -eq 0 ]; then
    echo "创建 Docker 网络: $net_name" > /dev/tty
    docker network create "$net_name"
  else
    echo "Docker 网络 $net_name 已存在，跳过创建" > /dev/tty
  fi
}

#-------------------------------------------------------------------------------
# 函数名: remove_docker_network
# 功能: 删除 Docker 网络（如果存在）
# 参数: $1 - 网络名称
# 返回: 无
#-------------------------------------------------------------------------------
remove_docker_network() {
  local net_name="$1"
  local net_flag

  # 检查网络是否存在
  net_flag=$(docker network ls | grep -w "$net_name" | wc -l)
  if [ $net_flag -ne 0 ]; then
    echo "删除 Docker 网络: $net_name" > /dev/tty
    docker network rm "$net_name"
  else
    echo "Docker 网络 $net_name 不存在，跳过删除" > /dev/tty
  fi
}

#-------------------------------------------------------------------------------
# 函数名: create_docker_volume
# 功能: 创建 Docker 命名卷（如果不存在）
# 参数: $1 - 数据卷名称
# 返回: 无
#-------------------------------------------------------------------------------
create_docker_volume() {
  local vol_name="$1"
  local vol_flag

  # 检查数据卷是否已存在
  vol_flag=$(docker volume ls -f name="$vol_name" | grep -w "$vol_name\$" | wc -l)
  if [ $vol_flag -eq 0 ]; then
    echo "创建 Docker 数据卷: $vol_name" > /dev/tty
    docker volume create --name="$vol_name"
  else
    echo "Docker 数据卷 $vol_name 已存在，跳过创建" > /dev/tty
  fi
}

#-------------------------------------------------------------------------------
# 函数名: remove_docker_volume
# 功能: 删除 Docker 命名卷（如果存在）
# 参数: $1 - 数据卷名称
# 返回: 无
#-------------------------------------------------------------------------------
remove_docker_volume() {
  local vol_name="$1"
  local vol_flag

  # 检查数据卷是否存在
  vol_flag=$(docker volume ls -f name="$vol_name" | grep -w "$vol_name\$" | wc -l)
  if [ $vol_flag -ne 0 ]; then
    echo "删除 Docker 数据卷: $vol_name" > /dev/tty
    docker volume rm "$vol_name"
  else
    echo "Docker 数据卷 $vol_name 不存在，跳过删除" > /dev/tty
  fi
}

#-------------------------------------------------------------------------------
# 函数名: remove_docker_volumes_safely
# 功能: 安全删除指定的Docker数据卷（支持通配符）
# 参数: $@ - 要删除的数据卷名称列表（支持通配符如 base-gauss*）
# 返回: 无
# 说明: 检查数据卷是否存在和被使用，询问用户确认后先删除关联容器再删除数据卷
#-------------------------------------------------------------------------------
remove_docker_volumes_safely() {
  local volume_patterns=("$@")

  if [ ${#volume_patterns[@]} -eq 0 ]; then
    echo_error "请指定要删除的数据卷名称或模式"
    echo "用法: $PROGNAME docker rmvol <volume1> [volume2] [pattern*] [...]" > /dev/tty
    echo "示例: $PROGNAME docker rmvol base-gauss* my-volume" > /dev/tty
    return 1
  fi

  echo_yellow "开始检查指定的 Docker 数据卷..."

  local volumes_to_remove=()
  local containers_to_remove=()

  # 展开通配符模式并检查每个数据卷的状态
  for pattern in "${volume_patterns[@]}"; do
    echo "处理模式: $pattern" > /dev/tty

    # 如果包含通配符，则进行模式匹配
    if [[ "$pattern" == *"*"* ]]; then
      # 获取所有数据卷并进行模式匹配
      local matched_volumes=$(docker volume ls --format "{{.Name}}" | grep "^${pattern//\*/.*}$" 2>/dev/null || true)

      if [ -z "$matched_volumes" ]; then
        echo "  没有找到匹配模式 '$pattern' 的数据卷" > /dev/tty
        continue
      fi

      echo "  找到匹配的数据卷:" > /dev/tty
      while IFS= read -r volume_name; do
        [ -z "$volume_name" ] && continue
        echo "    - $volume_name" > /dev/tty

        # 检查是否已在删除列表中
        if [[ ! " ${volumes_to_remove[*]} " =~ " ${volume_name} " ]]; then
          volumes_to_remove+=("$volume_name")
        fi

        # 检查数据卷是否被容器使用
        local containers_using=$(docker ps -a --filter volume="$volume_name" --format "{{.Names}}" 2>/dev/null)

        if [ -n "$containers_using" ]; then
          echo "      正被以下容器使用:" > /dev/tty
          echo "$containers_using" | sed 's/^/        - /' > /dev/tty
          # 将容器添加到删除列表（避免重复）
          while IFS= read -r container; do
            if [[ ! " ${containers_to_remove[*]} " =~ " ${container} " ]]; then
              containers_to_remove+=("$container")
            fi
          done <<< "$containers_using"
        else
          echo "      未被使用" > /dev/tty
        fi
      done <<< "$matched_volumes"
    else
      # 精确匹配单个数据卷
      echo "检查数据卷: $pattern" > /dev/tty

      # 检查数据卷是否存在
      if ! docker volume inspect "$pattern" >/dev/null 2>&1; then
        echo "  数据卷 $pattern 不存在，跳过" > /dev/tty
        continue
      fi

      volumes_to_remove+=("$pattern")

      # 检查数据卷是否被容器使用
      local containers_using=$(docker ps -a --filter volume="$pattern" --format "{{.Names}}" 2>/dev/null)

      if [ -n "$containers_using" ]; then
        echo "  数据卷 $pattern 正被以下容器使用:" > /dev/tty
        echo "$containers_using" | sed 's/^/    - /' > /dev/tty
        # 将容器添加到删除列表（避免重复）
        while IFS= read -r container; do
          if [[ ! " ${containers_to_remove[*]} " =~ " ${container} " ]]; then
            containers_to_remove+=("$container")
          fi
        done <<< "$containers_using"
      else
        echo "  数据卷 $pattern 未被使用" > /dev/tty
      fi
    fi
  done

  # 如果没有数据卷需要删除
  if [ ${#volumes_to_remove[@]} -eq 0 ]; then
    echo_yellow "没有可删除的数据卷"
    return 0
  fi

  # 显示删除清单
  echo_yellow "删除清单："
  echo "将要删除的数据卷：" > /dev/tty
  for volume_name in "${volumes_to_remove[@]}"; do
    echo "  - $volume_name" > /dev/tty
  done

  if [ ${#containers_to_remove[@]} -gt 0 ]; then
    echo "将要删除的关联容器：" > /dev/tty
    for container_name in "${containers_to_remove[@]}"; do
      echo "  - $container_name" > /dev/tty
    done
  fi

  # 最终确认
  echo_yellow "警告：此操作将永久删除上述容器和数据卷中的所有数据！"
  echo_yellow "请输入 'yes' 确认删除："
  read -r confirmation

  if [[ "$confirmation" != "yes" ]]; then
    echo "操作已取消" > /dev/tty
    return 0
  fi

  # 先删除容器
  if [ ${#containers_to_remove[@]} -gt 0 ]; then
    echo_yellow "开始删除关联容器..."
    for container_name in "${containers_to_remove[@]}"; do
      echo_yellow "删除容器: $container_name" > /dev/tty
      if docker rm -f "$container_name" 2>/dev/null; then
        echo "  ✓ 删除成功" > /dev/tty
      else
        echo "  ✗ 删除失败" > /dev/tty
      fi
    done
  fi

  # 再删除数据卷
  echo_yellow "开始删除数据卷..."
  for volume_name in "${volumes_to_remove[@]}"; do
    echo_yellow "删除数据卷: $volume_name"
    if docker volume rm "$volume_name" 2>/dev/null; then
      echo "  ✓ 删除成功" > /dev/tty
    else
      echo "  ✗ 删除失败" > /dev/tty
    fi
  done

  echo_yellow "数据卷删除操作完成！"
}

#-------------------------------------------------------------------------------
# 函数名: backup_docker_volume
# 功能: 备份 Docker 命名卷数据
# 参数: $1 - 数据卷名称
# 返回: 无
# 依赖: BACKUP_PATH, DOCKER_BUSY_BOX_IMAGE 环境变量
#-------------------------------------------------------------------------------
backup_docker_volume() {
  local vol_name="$1"
  local vol_flag
  local backup_file_name

  echo_yellow "备份 Docker 数据卷: $vol_name"

  # 检查数据卷是否存在
  vol_flag=$(docker volume ls -f name="$vol_name" | grep -w "$vol_name\$" | wc -l)
  if [ $vol_flag -eq 1 ]; then
    # 生成备份文件名（包含时间戳）
    backup_file_name="$vol_name.$(date "+%H.%M.%S").tgz"

    # 使用临时容器执行备份
    docker run --rm \
      --mount source="$vol_name",target=/backup_source,readonly \
      -v "$BACKUP_PATH":/backup_dist \
      "${DOCKER_BUSY_BOX_IMAGE}" tar zcvf "/backup_dist/$backup_file_name" /backup_source

    echo_yellow "数据卷 $vol_name 备份完成: $backup_file_name"
  else
    echo_error "数据卷 $vol_name 不存在，备份失败"
  fi
}

#-------------------------------------------------------------------------------
# 函数名: restore_docker_volume
# 功能: 从备份文件恢复 Docker 命名卷数据
# 参数: $1 - 数据卷名称
#       $2 - 备份文件路径
# 返回: 无
# 注意: 此操作会覆盖目标数据卷的现有数据
#-------------------------------------------------------------------------------
restore_docker_volume() {
  local vol_name="$1"
  local restore_from="$2"

  echo_yellow "恢复 Docker 数据卷: $vol_name"
  echo_yellow "从备份文件: $restore_from"

  # 使用临时容器执行恢复
  docker run -i --rm --name restore_helper \
    --mount source="$vol_name",target=/restore_target \
    -v "$restore_from":/restore_source/backup.tgz \
    "${DOCKER_BUSY_BOX_IMAGE}" tar zxvf /restore_source/backup.tgz --directory /restore_target/ --strip 1

  echo_yellow "数据卷 $vol_name 恢复完成"
}

#-------------------------------------------------------------------------------
# 函数名: pull_docker_image
# 功能: 拉取 Docker Compose 服务的镜像
# 参数: 无
# 返回: 0 - 成功，1 - 失败
#-------------------------------------------------------------------------------
pull_docker_image() {
  docker_compose_cmd pull && return 0 || return 1
}

#-------------------------------------------------------------------------------
# 函数名: docker_compose_pull_with_retry
# 功能: 带重试机制的 Docker 镜像拉取
# 参数: 无
# 返回: 0 - 成功，1 - 失败
# 说明: 最多重试3次，失败时返回错误
#-------------------------------------------------------------------------------
docker_compose_pull_with_retry() {
  # 离线模式下跳过镜像拉取
  if [ "$OFFLINE_MODE" = true ]; then
    echo_yellow "离线模式：跳过镜像拉取"
    return 0
  fi

  local i

  for i in {1..3}; do
    echo "尝试拉取镜像 (第 $i 次)..." > /dev/tty

    if pull_docker_image; then
      echo_yellow "镜像拉取成功"
      return 0
    else
      if [ "$i" = 3 ]; then
        echo_error "拉取镜像失败，已重试 3 次"
        return 1
      fi
      echo_yellow "拉取失败，准备重试..."
      sleep 2
    fi
  done
}

#-------------------------------------------------------------------------------
# 函数名: docker_compose_up
# 功能: 启动 Docker Compose 服务
# 参数: $1 - Docker Compose 文件所在目录路径
# 返回: 0 - 成功，1 - 失败
#-------------------------------------------------------------------------------
docker_compose_up() {
  local compose_path="$1"

  # 参数验证
  if [ -z "$compose_path" ]; then
    echo_error "docker compose 缺少路径参数"
    return 1
  fi

  echo_yellow "启动 Docker Compose 服务: $compose_path"
  cd "$compose_path" && docker_compose_cmd up -d
  # 注意: 如需要可以在此处添加 docker_compose_pull_with_retry
}

#-------------------------------------------------------------------------------
# 函数名: docker_compose_down
# 功能: 停止并删除 Docker Compose 服务容器
# 参数: $1 - Docker Compose 文件所在目录路径
# 返回: 0 - 成功，1 - 失败
# 注意: 此操作会删除容器，但保留数据卷
#-------------------------------------------------------------------------------
docker_compose_down() {
  local compose_path="$1"

  # 参数验证
  if [ -z "$compose_path" ]; then
    echo_error "docker compose 缺少路径参数"
    return 1
  fi

  echo_yellow "停止并删除 Docker Compose 服务: $compose_path"
  cd "$compose_path" && docker_compose_cmd down
}

#-------------------------------------------------------------------------------
# 函数名: docker_compose_start
# 功能: 启动 Docker Compose 服务
# 参数: $1 - Docker Compose 文件所在目录路径
# 返回: 0 - 成功，1 - 失败
#-------------------------------------------------------------------------------
docker_compose_start() {
  local compose_path="$1"

  # 参数验证
  if [ -z "$compose_path" ]; then
    echo_error "docker compose 缺少路径参数"
    return 1
  fi

  echo_yellow "启动 Docker Compose 服务: $compose_path"
  cd "$compose_path" && docker_compose_cmd start
}

#-------------------------------------------------------------------------------
# 函数名: docker_compose_stop
# 功能: 停止 Docker Compose 服务
# 参数: $1 - Docker Compose 文件所在目录路径
# 返回: 0 - 成功，1 - 失败
#-------------------------------------------------------------------------------
docker_compose_stop() {
  local compose_path="$1"

  # 参数验证
  if [ -z "$compose_path" ]; then
    echo_error "docker compose 缺少路径参数"
    return 1
  fi

  echo_yellow "停止 Docker Compose 服务: $compose_path"
  cd "$compose_path" && docker_compose_cmd stop
}

#-------------------------------------------------------------------------------
# 函数名: docker_compose_restart
# 功能: 重启 Docker Compose 服务
# 参数: $1 - Docker Compose 文件所在目录路径
# 返回: 0 - 成功，1 - 失败
#-------------------------------------------------------------------------------
docker_compose_restart() {
  local compose_path="$1"

  # 参数验证
  if [ -z "$compose_path" ]; then
    echo_error "docker compose 缺少路径参数"
    return 1
  fi

  echo_yellow "重启 Docker Compose 服务: $compose_path"
  cd "$compose_path" && docker_compose_cmd restart
  # 注意: 如需要可以在此处添加 docker_compose_pull_with_retry
}

#-------------------------------------------------------------------------------
# 函数名: init_docker
# 功能: 初始化 Docker 环境
# 参数: 无
# 返回: 无
# 说明: 配置系统参数、登录镜像仓库、创建网络和数据卷
#-------------------------------------------------------------------------------
init_docker() {
  echo_yellow "初始化 Docker 环境..."

  # 配置系统参数：禁用 swap 以提高性能（仅在Linux系统上执行）
  if [[ "$OSTYPE" == "linux-gnu"* ]]; then
    if ! grep -q "^vm.swappiness=0" /etc/sysctl.conf 2>/dev/null; then
      echo "vm.swappiness=0" >> /etc/sysctl.conf
      echo "已添加 vm.swappiness=0 配置" > /dev/tty
    else
      echo "vm.swappiness=0 配置已存在，跳过添加" > /dev/tty
    fi
    sysctl -w vm.swappiness=0 >/dev/null 2>&1
  else
    echo "非Linux系统，跳过 vm.swappiness 配置" > /dev/tty
  fi

  # Harbor 镜像仓库登录（仅在非离线模式下执行）
  if [ "$OFFLINE_MODE" = false ]; then
    echo_yellow "正在登录 Harbor 镜像仓库..."
    "$DEPLOY_PATH/tools/harbor2-login-deploy.sh.x" &
  else
    echo_yellow "离线模式：跳过 Harbor 镜像仓库登录"
  fi

  # 创建 Docker 网络
  echo_yellow "正在创建 Docker 网络..."
  for network_name in "${DOCKER_NETWORKS[@]}"; do
    create_docker_network "$network_name"
  done

  # 创建 Docker 命名卷
  echo_yellow "正在创建 Docker 命名卷..."
  for volume_name in "${DOCKER_VOLUMES[@]}"; do
    create_docker_volume "$volume_name"
  done

  echo_yellow "Docker 环境初始化完成！"
}

#-------------------------------------------------------------------------------
# 函数名: prune_docker
# 功能: 清理 Docker 资源（删除所有网络和数据卷）
# 参数: 无
# 返回: 无
# 警告: 此操作会删除所有配置的网络和数据卷，数据将丢失
#-------------------------------------------------------------------------------
prune_docker() {
  echo_yellow "开始清理 Docker 资源..."

  # 删除 Docker 命名卷
  echo_yellow "正在删除 Docker 命名卷..."
  for volume_name in "${DOCKER_VOLUMES[@]}"; do
    remove_docker_volume "$volume_name"
  done

  # 删除 Docker 网络
  echo_yellow "正在删除 Docker 网络..."
  for network_name in "${DOCKER_NETWORKS[@]}"; do
    remove_docker_network "$network_name"
  done

  echo_yellow "Docker 资源清理完成！"
}
