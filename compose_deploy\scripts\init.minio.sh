#!/bin/bash

#===============================================================================
# MinIO 对象存储初始化脚本
#===============================================================================
# 功能描述: 初始化 MinIO 对象存储服务的资源文件
# 执行环境: 在 compose_deploy.sh 的 init_modules 函数中被调用
# 依赖服务: bladex-minio Docker 数据卷
# 创建时间: 2024年
#
# 主要功能:
# - 将预置的资源文件复制到 MinIO 数据卷中
# - 为系统提供默认的对象存储内容
# - 确保 MinIO 服务具备必要的初始数据
#===============================================================================

# 遇到错误立即退出
set -e

#-------------------------------------------------------------------------------
# 函数名: init_minio_resources
# 功能: 初始化 MinIO 对象存储的资源文件
# 参数: 无
# 返回: 无
# 说明: 将本地 minio 目录中的资源文件复制到 MinIO Docker 数据卷中
#       确保 MinIO 服务启动时具备必要的初始数据和配置
#-------------------------------------------------------------------------------
init_minio_resources() {
  local source_dir
  local target_dir
  local current_dir

  # 保存当前目录
  current_dir=$(pwd)

  # 切换到基础服务目录
  if [ -z "${BASE_SERVICE_PATH}" ]; then
    echo_error "BASE_SERVICE_PATH 环境变量未设置"
    return 1
  fi

  echo_yellow "开始 MinIO 资源文件初始化..."
  cd "${BASE_SERVICE_PATH}" || {
    echo_error "无法切换到基础服务目录: ${BASE_SERVICE_PATH}"
    return 1
  }

  # 检查源文件目录是否存在
  source_dir="./minio"
  if [ ! -d "${source_dir}" ]; then
    echo_error "MinIO 资源文件目录不存在: ${source_dir}"
    cd "${current_dir}"
    return 1
  fi

  # 获取 MinIO Docker 数据卷的挂载点
  echo "[INFO]: 获取 MinIO 数据卷挂载点..."
  target_dir=$(docker volume inspect --format '{{ .Mountpoint }}' bladex-minio 2>/dev/null)
  if [ $? -ne 0 ] || [ -z "${target_dir}" ]; then
    echo_error "无法获取 bladex-minio 数据卷信息，请确保数据卷已创建"
    cd "${current_dir}"
    return 1
  fi

  echo "[INFO]: 源目录: ${source_dir}"
  echo "[INFO]: 目标目录: ${target_dir}"

  # 检查目标目录是否可写
  if [ ! -w "${target_dir}" ]; then
    echo_error "目标目录不可写: ${target_dir}"
    cd "${current_dir}"
    return 1
  fi

  # 复制资源文件到 MinIO 数据卷
  echo "[INFO]: 正在复制 MinIO 资源文件..."
  if yes | cp -r "${source_dir}"/* "${target_dir}/" 2>/dev/null; then
    echo_ok "MinIO 资源文件复制成功"
  else
    echo_error "MinIO 资源文件复制失败"
    cd "${current_dir}"
    return 1
  fi

  # 设置正确的文件权限
  echo "[INFO]: 设置文件权限..."
  if chmod -R 755 "${target_dir}" 2>/dev/null; then
    echo "[INFO]: 文件权限设置完成"
  else
    echo_yellow "警告: 无法设置文件权限，MinIO 服务可能需要手动调整权限"
  fi

  echo_yellow "MinIO 资源文件初始化完成！"

  # 切换到 MOS 服务目录（保持原有行为）
  if [ -n "${MOS_SERVICE_PATH}" ]; then
    cd "${MOS_SERVICE_PATH}" || {
      echo_yellow "警告: 无法切换到 MOS 服务目录: ${MOS_SERVICE_PATH}"
      cd "${current_dir}"
    }
  else
    cd "${current_dir}"
  fi
}

# 执行 MinIO 资源初始化
init_minio_resources
