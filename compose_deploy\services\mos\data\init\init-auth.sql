/*
 Navicat Premium Data Transfer

 Source Server         : *************
 Source Server Type    : PostgreSQL
 Source Server Version : 100011
 Source Host           : *************:5432
 Source Catalog        : gis
 Source Schema         : authclient

 Target Server Type    : PostgreSQL
 Target Server Version : 100011
 File Encoding         : 65001

 Date: 28/04/2022 17:37:17
*/

------------------------------
--  recreate schema authclient
------------------------------
-- drop SCHEMA if exists authclient;
-- create schema authclient;

-- ----------------------------
-- Sequence structure for auth_record_child_mode_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "authclient"."auth_record_child_mode_id_seq";
CREATE SEQUENCE "authclient"."auth_record_child_mode_id_seq"
    INCREMENT 1
MINVALUE  1
MAXVALUE 2147483647
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for auth_record_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "authclient"."auth_record_id_seq";
CREATE SEQUENCE "authclient"."auth_record_id_seq"
    INCREMENT 1
MINVALUE  1
MAXVALUE 2147483647
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for auth_registry_rel_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "authclient"."auth_registry_rel_id_seq";
CREATE SEQUENCE "authclient"."auth_registry_rel_id_seq"
    INCREMENT 1
MINVALUE  1
MAXVALUE 2147483647
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for auth_status_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "authclient"."auth_status_id_seq";
CREATE SEQUENCE "authclient"."auth_status_id_seq"
    INCREMENT 1
MINVALUE  1
MAXVALUE 2147483647
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for device_info_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "authclient"."device_info_id_seq";
CREATE SEQUENCE "authclient"."device_info_id_seq"
    INCREMENT 1
MINVALUE  1
MAXVALUE 2147483647
START 1
CACHE 1;

-- ----------------------------
-- Table structure for auth_record
-- ----------------------------
DROP TABLE IF EXISTS "authclient"."auth_record";
CREATE TABLE "authclient"."auth_record" (
                                            "id" int4 NOT NULL DEFAULT nextval('"authclient".auth_record_id_seq'::regclass),
                                            "salt" varchar(50) COLLATE "pg_catalog"."default",
                                            "p_name" varchar(50) COLLATE "pg_catalog"."default",
                                            "sign" varchar(200) COLLATE "pg_catalog"."default",
                                            "dim_id" int8,
                                            "device_id" varchar(50) COLLATE "pg_catalog"."default",
                                            "exp" varchar(50) COLLATE "pg_catalog"."default",
                                            "days" varchar(50) COLLATE "pg_catalog"."default",
                                            "s_name" varchar(50) COLLATE "pg_catalog"."default",
                                            "tid" varchar(50) COLLATE "pg_catalog"."default",
                                            "sid" varchar(50) COLLATE "pg_catalog"."default",
                                            "primary_id" varchar(50) COLLATE "pg_catalog"."default",
                                            "codetext_json" text COLLATE "pg_catalog"."default",
                                            "create_time" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
                                            "message" varchar(50) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "authclient"."auth_record"."id" IS '自增id';
COMMENT ON COLUMN "authclient"."auth_record"."salt" IS '盐值';
COMMENT ON COLUMN "authclient"."auth_record"."p_name" IS '项目名称';
COMMENT ON COLUMN "authclient"."auth_record"."sign" IS '签名';
COMMENT ON COLUMN "authclient"."auth_record"."dim_id" IS '维度id';
COMMENT ON COLUMN "authclient"."auth_record"."device_id" IS '设备id';
COMMENT ON COLUMN "authclient"."auth_record"."exp" IS '过期时间';
COMMENT ON COLUMN "authclient"."auth_record"."days" IS '天数';
COMMENT ON COLUMN "authclient"."auth_record"."s_name" IS '服务name';
COMMENT ON COLUMN "authclient"."auth_record"."tid" IS '组织id';
COMMENT ON COLUMN "authclient"."auth_record"."sid" IS '服务id';
COMMENT ON COLUMN "authclient"."auth_record"."primary_id" IS '主键';
COMMENT ON COLUMN "authclient"."auth_record"."codetext_json" IS '自增id';
COMMENT ON COLUMN "authclient"."auth_record"."create_time" IS '创建时间';
COMMENT ON COLUMN "authclient"."auth_record"."message" IS '描述';
COMMENT ON TABLE "authclient"."auth_record" IS '记录表';

-- ----------------------------
-- Table structure for auth_record_child_mode
-- ----------------------------
DROP TABLE IF EXISTS "authclient"."auth_record_child_mode";
CREATE TABLE "authclient"."auth_record_child_mode" (
                                                       "id" int4 NOT NULL DEFAULT nextval('"authclient".auth_record_child_mode_id_seq'::regclass),
                                                       "authorization_id" int8,
                                                       "service_child_name" varchar(50) COLLATE "pg_catalog"."default",
                                                       "service_child_id" varchar(50) COLLATE "pg_catalog"."default",
                                                       "pexpire_time" varchar(50) COLLATE "pg_catalog"."default",
                                                       "days" varchar(50) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "authclient"."auth_record_child_mode"."id" IS '自增id';
COMMENT ON COLUMN "authclient"."auth_record_child_mode"."authorization_id" IS '授权id';
COMMENT ON COLUMN "authclient"."auth_record_child_mode"."service_child_name" IS '子服务名称';
COMMENT ON COLUMN "authclient"."auth_record_child_mode"."service_child_id" IS '子服务id';
COMMENT ON COLUMN "authclient"."auth_record_child_mode"."pexpire_time" IS '过期时间';
COMMENT ON COLUMN "authclient"."auth_record_child_mode"."days" IS '剩余天数';
COMMENT ON TABLE "authclient"."auth_record_child_mode" IS '子模块表';

-- ----------------------------
-- Table structure for auth_registry_rel
-- ----------------------------
DROP TABLE IF EXISTS "authclient"."auth_registry_rel";
CREATE TABLE "authclient"."auth_registry_rel" (
                                                  "id" int4 NOT NULL DEFAULT nextval('"authclient".auth_registry_rel_id_seq'::regclass),
                                                  "name" varchar(50) COLLATE "pg_catalog"."default",
                                                  "value" varchar(50) COLLATE "pg_catalog"."default",
                                                  "p_id" varchar(50) COLLATE "pg_catalog"."default",
                                                  "p_name" varchar(50) COLLATE "pg_catalog"."default",
                                                  "create_time" varchar(50) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "authclient"."auth_registry_rel"."id" IS '自增id';
COMMENT ON COLUMN "authclient"."auth_registry_rel"."name" IS '模块名称';
COMMENT ON COLUMN "authclient"."auth_registry_rel"."value" IS '模块code';
COMMENT ON COLUMN "authclient"."auth_registry_rel"."p_id" IS '预留';
COMMENT ON COLUMN "authclient"."auth_registry_rel"."p_name" IS '预留';
COMMENT ON COLUMN "authclient"."auth_registry_rel"."create_time" IS '心跳时间';
COMMENT ON TABLE "authclient"."auth_registry_rel" IS '子模块表';

-- ----------------------------
-- Table structure for auth_status
-- ----------------------------
DROP TABLE IF EXISTS "authclient"."auth_status";
CREATE TABLE "authclient"."auth_status" (
                                            "id" int4 NOT NULL DEFAULT nextval('"authclient".auth_status_id_seq'::regclass),
                                            "authorization_name" varchar(500) COLLATE "pg_catalog"."default",
                                            "service_name" varchar(500) COLLATE "pg_catalog"."default",
                                            "expire_time" varchar(500) COLLATE "pg_catalog"."default",
                                            "days" varchar(500) COLLATE "pg_catalog"."default",
                                            "status" varchar(1) COLLATE "pg_catalog"."default",
                                            "create_time" varchar(500) COLLATE "pg_catalog"."default",
                                            "project_name" varchar(500) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "authclient"."auth_status"."id" IS '自增id';
COMMENT ON COLUMN "authclient"."auth_status"."authorization_name" IS '授权名称';
COMMENT ON COLUMN "authclient"."auth_status"."service_name" IS '服务名称';
COMMENT ON COLUMN "authclient"."auth_status"."expire_time" IS '过期时间';
COMMENT ON COLUMN "authclient"."auth_status"."days" IS '日期';
COMMENT ON COLUMN "authclient"."auth_status"."status" IS '状态';
COMMENT ON COLUMN "authclient"."auth_status"."create_time" IS '创建时间';
COMMENT ON COLUMN "authclient"."auth_status"."project_name" IS '项目名称';
COMMENT ON TABLE "authclient"."auth_status" IS '授权状态表';

-- ----------------------------
-- Table structure for device_info
-- ----------------------------
DROP TABLE IF EXISTS "authclient"."device_info";
CREATE TABLE "authclient"."device_info" (
                                            "id" int4 NOT NULL DEFAULT nextval('"authclient".device_info_id_seq'::regclass),
                                            "device_code" varchar(500) COLLATE "pg_catalog"."default",
                                            "mac" varchar(500) COLLATE "pg_catalog"."default",
                                            "system_version" varchar(500) COLLATE "pg_catalog"."default",
                                            "is_del" varchar(1) COLLATE "pg_catalog"."default",
                                            "create_time" varchar(50) COLLATE "pg_catalog"."default",
                                            "message" varchar(50) COLLATE "pg_catalog"."default",
                                            "cpu" varchar(500) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "authclient"."device_info"."id" IS '自增id';
COMMENT ON COLUMN "authclient"."device_info"."device_code" IS '设备code';
COMMENT ON COLUMN "authclient"."device_info"."mac" IS 'mac';
COMMENT ON COLUMN "authclient"."device_info"."system_version" IS '系统版本';
COMMENT ON COLUMN "authclient"."device_info"."is_del" IS '是否删除';
COMMENT ON COLUMN "authclient"."device_info"."create_time" IS '创建时间';
COMMENT ON COLUMN "authclient"."device_info"."message" IS '描述';
COMMENT ON COLUMN "authclient"."device_info"."cpu" IS 'cpu';
COMMENT ON TABLE "authclient"."device_info" IS '设备信息表';

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
SELECT setval('"authclient"."auth_record_child_mode_id_seq"', 35, true);
SELECT setval('"authclient"."auth_record_id_seq"', 38, true);
SELECT setval('"authclient"."auth_registry_rel_id_seq"', 8039, true);
SELECT setval('"authclient"."auth_status_id_seq"', 38, true);
SELECT setval('"authclient"."device_info_id_seq"', 36, true);

-- ----------------------------
-- Primary Key structure for table auth_record
-- ----------------------------
ALTER TABLE "authclient"."auth_record" ADD CONSTRAINT "auth_record_pkey" PRIMARY KEY ("create_time");
