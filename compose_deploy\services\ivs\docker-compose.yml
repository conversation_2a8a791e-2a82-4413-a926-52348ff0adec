# =============================================================================
# YAML 模板定义 (按功能分组)
# =============================================================================

# IVS 视频服务基础模板
x-video-defaults: &video_defaults
  restart: unless-stopped
  logging:
    driver: "json-file"
    options:
      max-size: "1g"
      max-file: "2"
  env_file:
    - ../base/.env
  networks:
    - middle

# =============================================================================
# 网络和存储卷定义
# =============================================================================

networks:
  # 视频服务网络 - 与其他视频相关服务共享
  middle:
    external: true
    name: ${DOCKER_NETWORK}

volumes:
  # IVS PostgreSQL 数据存储
  ivs-postgres:
    external: true
  # IVS 后端服务日志存储
  ivs-backend-log:
    external: true

# =============================================================================
# 服务定义 (按 IVS 系统架构和依赖关系排序)
# =============================================================================

services:
  # ---------------------------------------------------------------------------
  # 1. 数据存储层 - PostgreSQL 数据库服务
  # ---------------------------------------------------------------------------

  # IVS PostgreSQL 数据库 (支持 PostGIS 地理信息扩展)
  postgres:
    <<: *video_defaults
    image: harbor2.qdbdtd.com:8088/middleware/postgis:${PG_VERSION}
    container_name: ivs_postgres
    environment:
      # 数据库初始化配置
      - POSTGRES_USER=${POSTGRES_USERNAME}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_DB=${POSTGRES_DATABASE}
    volumes:
      # 数据持久化存储
      - "ivs-postgres:/var/lib/postgresql/data"
      # 数据库初始化脚本
      - "./init.sql:/docker-entrypoint-initdb.d/postgres_init.sql:ro"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USERNAME} -d ${POSTGRES_DATABASE}"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

  # ---------------------------------------------------------------------------
  # 2. 应用服务层 - IVS 智能视频监控服务
  # ---------------------------------------------------------------------------

  # IVS 后端服务 (智能视频分析和监控)
  ivs-server:
    <<: *video_defaults
    image: harbor2.qdbdtd.com:8088/bjproduct/platform/services/ivs:${VERSION_BACKEND}
    container_name: ivs-server
    volumes:
      # 应用日志持久化存储
      - "ivs-backend-log:/home/<USER>/logs"
    depends_on:
      - postgres
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 90s
