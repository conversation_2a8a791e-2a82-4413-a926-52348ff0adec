# 运维工具脚本目录

本目录包含微服务运维相关的工具脚本，用于日常维护、备份恢复、镜像管理等操作。

## 📁 目录内容

### 📦 镜像管理工具

#### images_save.sh

**Docker 镜像批量导出工具**

**功能**：
- 将系统中所有有效的 Docker 镜像打包保存到 tar 文件
- 支持离线部署场景
- 自动排除无效镜像

**使用方法**：

```bash
chmod +x images_save.sh
./images_save.sh
# 生成多个独立的 .tar 文件到 /home/<USER>/ 目录
```

#### images_load_one.sh

**Docker 镜像单文件导入工具**

**功能**：
- 从单个 tar 文件导入镜像
- 简化的镜像导入操作

**使用方法**：

```bash
# 从 images.tar 导入镜像
chmod +x images_load_one.sh
./images_load_one.sh
```

#### images_save_one.sh

**Docker 镜像一键保存工具**

**功能**：
- 自动扫描所有本地镜像（排除 REPOSITORY 和 none）
- 一键保存所有有效镜像到单个 tar 文件
- 适用于快速镜像打包

**使用方法**：

```bash
chmod +x images_save_one.sh
./images_save_one.sh
# 生成 images.tar 文件
```

**特点**：
- 自动过滤无效镜像
- 生成单个压缩包文件
- 适合小规模镜像备份

### 🧹 系统清理工具

#### docker_clean.sh

**Docker 系统清理工具**

**功能**：

- 清理已退出的容器
- 删除名称为 none 的镜像
- 清理悬空数据卷
- 执行系统级资源清理
- 释放磁盘空间

**使用方法**：

```bash
chmod +x docker_clean.sh
./docker_clean.sh
```

**清理步骤**：
1. 停止并删除已退出的容器
2. 删除无标签镜像（名称为 none）
3. 清理悬空数据卷
4. 执行 `docker system prune --all --force`

#### 交互式数据卷删除

**通过主部署脚本进行精确的数据卷管理**

**功能**：

- 安全删除指定的 Docker 数据卷（支持通配符）
- 自动检查数据卷使用状态和关联容器
- 提供详细的删除清单和确认机制
- 支持批量删除多个数据卷和通配符匹配
- 先删除关联容器再删除数据卷，避免依赖冲突

**使用方法**：

```bash
# 删除单个数据卷
../compose_deploy.sh docker rmvol volume_name

# 删除多个数据卷
../compose_deploy.sh docker rmvol volume1 volume2 volume3

# 使用通配符删除匹配的数据卷
../compose_deploy.sh docker rmvol base-gauss* temp-*
```

**安全特性**：
- 检查数据卷是否存在
- 显示使用该数据卷的所有容器
- 要求用户输入 'yes' 确认删除
- 提供详细的操作反馈

### 🔄 代码管理工具

#### server_up.sh

**代码更新脚本**

**功能**：

- Git 代码拉取
- 本地修改保护
- 自动合并处理

**使用方法**：
```bash
chmod +x server_up.sh
./server_up.sh
```

### 🔐 认证工具

#### harbor-login-deploy.sh.x

**Harbor 镜像仓库登录工具（加密）**

**功能**：

- 自动登录私有镜像仓库
- 镜像拉取认证
- 部署时自动认证

#### harbor2-login-deploy.sh.x

**Harbor2 镜像仓库登录工具（加密）**

**功能**：

- Harbor 2.x 版本登录支持
- 增强的安全认证

### 🔧 恢复工具

#### restore/

**数据恢复工具目录**

**功能**：

- PostgreSQL 数据库恢复
- 版本升级 SQL 脚本
- 灾难恢复流程

**子目录结构**：

```
restore/
└── postgres/                    # PostgreSQL 恢复工具
    ├── README.md               # 详细恢复操作说明
    ├── docker-compose.yml      # 恢复专用容器编排
    ├── db/                     # 存放恢复用的 SQL 文件
    └── upgrade/                # 版本升级脚本
        ├── gis/                # GIS 服务升级脚本
        └── mes/                # MES 服务升级脚本
            ├── 20220829-mes-v1.2.41.sql
            └── 20220901-mes-v1.2.42.sql
```

**恢复流程**：
1. 删除并重新创建 `base-postgres` 命名卷
2. 创建初始化 SQL 硬连接
3. 复制备份的 SQL 文件到 `./db/` 目录
4. 通过 `docker-compose` 启动恢复服务
5. 查看日志确认恢复成功
6. 清理临时文件

## 🎯 使用场景

### 离线部署

```bash
# 在有网络的环境中
./images_save.sh

# 传输到离线环境
scp /home/<USER>/*.tar target-server:/path/
# 在离线环境中
find /path -name "*.tar" -exec docker load -i {} \;
# 或使用单文件导入（如果使用 images_save_one.sh 生成）
./images_load_one.sh
```

### 系统维护

```bash
# 清理 Docker 资源
./docker_clean.sh

# 精确删除指定数据卷
../compose_deploy.sh docker rmvol unused_volume1 unused_volume2

# 使用通配符批量删除
../compose_deploy.sh docker rmvol old-* temp-*

# 更新代码
./server_up.sh
```

### 数据恢复

```bash
# 进入恢复目录
cd restore/postgres/

# 按照 README.md 说明执行恢复操作
```

## ⚠️ 注意事项

### 镜像管理

- `images_save.sh` 生成的文件可能很大（几GB到几十GB）
- 确保有足够的磁盘空间（默认保存到 `/home/<USER>/`）
- 传输大文件时注意网络稳定性
- 支持增量保存，已存在的镜像文件会被跳过
- `images_save_one.sh` 生成单个 `images.tar` 文件，适合小规模备份

### 系统清理

- `docker_clean.sh` 会删除数据，请谨慎使用
- 建议在维护窗口期执行
- 清理前确保重要容器和镜像已备份
- 使用 `../compose_deploy.sh docker rmvol` 进行精确的数据卷删除
- 数据卷删除前会显示详细清单，需要用户确认

### 数据恢复

- 恢复操作会完全替换现有数据
- 操作前务必确认备份文件的完整性
- 恢复过程中服务需要停止

## 🔗 相关文档

- [主部署脚本说明](../compose_deploy.sh)
- [核心库脚本说明](../libs/README.md)
- [服务配置说明](../services/README.md)
- [Docker 镜像管理](https://docs.docker.com/engine/reference/commandline/images/)
- [PostgreSQL 备份恢复](https://www.postgresql.org/docs/current/backup.html)
- [Git 版本管理](https://git-scm.com/doc)

## 📝 工具脚本详细说明

### images_save.sh 详细功能

- **自动扫描**: 扫描本地所有 Docker 镜像
- **智能命名**: 自动生成安全的文件名（避免路径分隔符问题）
- **增量保存**: 跳过已存在的镜像文件
- **进度显示**: 显示保存进度和文件大小
- **错误处理**: 保存失败时提供详细错误信息

### 认证工具说明

- **harbor-login-deploy.sh.x**: 加密的 Harbor 登录脚本
- **harbor2-login-deploy.sh.x**: 支持 Harbor 2.x 版本的登录脚本
- 这些工具在部署时自动执行，无需手动调用

### 恢复工具使用

恢复工具位于 `restore/` 目录，主要用于：
- 数据库灾难恢复
- 版本升级时的数据迁移
- 开发环境数据重置

详细使用方法请参考 `restore/README.md`
