-- drop database if exists ivs;
-- create database ivs;
-- \c ivs;
create table public.ivs_domain_route_info (
  id serial,
  domain_type integer,
  is_agent integer,
  domain_code varchar(225),
  domain_name varchar(225),
  ip_type integer,
  ip varchar(225),
  domain_port integer,
  super_domain varchar(225),
  is_local_domain integer,
  reserve varchar(225),
  is_progression_transmit integer,
  is_online varchar(225)
);
comment on table public.ivs_domain_route_info is '域路由信息表';
comment on column public.ivs_domain_route_info.id is '自增id';
comment on column public.ivs_domain_route_info.domain_type is 'SMU工作模式：1堆叠、2集群、3单机';
comment on column public.ivs_domain_route_info.is_agent is '是否为代理：0否、1是';
comment on column public.ivs_domain_route_info.domain_code is '域编码，长度限制32字节';
comment on column public.ivs_domain_route_info.domain_name is '域名称';
comment on column public.ivs_domain_route_info.ip_type is 'IP地址类型：0：IP_V4,1：IP_V6';
comment on column public.ivs_domain_route_info.ip is 'IP地址，长度限制64字节';
comment on column public.ivs_domain_route_info.domain_port is '域端口';
comment on column public.ivs_domain_route_info.super_domain is '上级域编码';
comment on column public.ivs_domain_route_info.is_local_domain is '是否为本域：0否,1是';
comment on column public.ivs_domain_route_info.reserve is '保留字段';
comment on column public.ivs_domain_route_info.is_progression_transmit is '获取域路由扩展信息返回此字段,否则不返回此字段；是否是转发域：0否；1是';
comment on column public.ivs_domain_route_info.is_online is '是否在线：ON-在线 OFF-离线';
alter table public.ivs_domain_route_info owner to postgres;
-- 主设备信息表
create table public.ivs_master_device_info (
  id serial,
  protocol_type varchar(225),
  login_type integer,
  enable_schedule integer,
  status integer,
  image_dev_status integer,
  master_domain_code varchar(225),
  disaster_domain_code varchar(225),
  reserve varchar(225),
  delete_status integer
);
comment on table public.ivs_master_device_info is '主设备信息表';
comment on column public.ivs_master_device_info.id is '自增id';
comment on column public.ivs_master_device_info.protocol_type is '主设备协议类型，长度限制64字节：● TEYES：千里眼● ONVIF● HWSDK：华为SDK● HIKSDK：海康SDK';
comment on column public.ivs_master_device_info.login_type is '认证类型：protocolType为ONVIF时，认证类型有如下三种：– 0：None– 1：HTTP Digest– 2：WS-Username token Authentication● protocolType不是ONVIF时，取值为0：None';
comment on column public.ivs_master_device_info.enable_schedule is '是否启用集群调度：● 0：不启用● 1：启用，此时NVRCode不能为空。说明：该字段暂不起作用';
comment on column public.ivs_master_device_info.status is '设备状态：● 0：离线● 1：在线● 2：休眠';
comment on column public.ivs_master_device_info.image_dev_status is '设备状态：（图片接入）● 0：离线● 1：在线';
comment on column public.ivs_master_device_info.master_domain_code is '生产域';
comment on column public.ivs_master_device_info.disaster_domain_code is '容灾域';
comment on column public.ivs_master_device_info.reserve is '保留字段。长度限制32字节，必须保留该字段，字段内容可以置空';
alter table public.ivs_master_device_info owner to postgres;
-- 主设备基本信息表
create table public.ivs_master_device_basic_info (
  id serial,
  ivs_master_device_info_id integer,
  type integer,
  serial_number varchar(225),
  code varchar(225),
  name varchar(225),
  parent_code varchar(225),
  domain_code varchar(225),
  vendor_type varchar(225),
  model varchar(225),
  ip_type integer,
  ip varchar(225),
  port integer,
  connect_code varchar(225),
  unified_access_code varchar(225),
  img_dev_parent_code varchar(225),
  img_protocol_type varchar(225),
  img_user_name varchar(225),
  img_gw_code varchar(225),
  collection_dev_type varchar(225),
  active_status integer,
  is_nat integer,
  reserve varchar(225)
);
comment on table public.ivs_master_device_basic_info is '主设备基本信息';
comment on column public.ivs_master_device_basic_info.id is '自增id';
comment on column public.ivs_master_device_basic_info.ivs_master_device_info_id is '关联主设备信息表id';
comment on column public.ivs_master_device_basic_info.type is '主设备类型：1：IPC、2：DVS、3：DVR、4：eNVR';
comment on column public.ivs_master_device_basic_info.serial_number is '序列号（SN/IMEI），长度限制64字节';
comment on column public.ivs_master_device_basic_info.code is '设备编码例如：32010300100201030000#6bdacabae9c546e9ab5b8688ccd85a59，长度限制64字节，详见设备编码规则';
comment on column public.ivs_master_device_basic_info.name is '设备名称键盘可见字符和中文，长度限制128字节';
comment on column public.ivs_master_device_basic_info.parent_code is '父设备编码：为空：没有父设备；非空：表示该设备被eNVR管理。例如：32010300100204010000#6bdacabae9c546e9ab5b8688ccd85a59，长度限制64字节，详见设备编码规则';
comment on column public.ivs_master_device_basic_info.domain_code is '域编码，例如：6bdacabae9c546e9ab5b8688ccd85a59，长度限制32字节';
comment on column public.ivs_master_device_basic_info.vendor_type is '设备提供商类型：（长度限制32字节）● HUAWEI● HIK● DAHUA● SUNELL● CANON● CHANGHONG● TIANDY● PANASONIC● AXIS';
comment on column public.ivs_master_device_basic_info.model is '设备型号由各设备厂家提供，长度限制32字节';
comment on column public.ivs_master_device_basic_info.ip_type is 'P地址类型：0：IP_V4,1：IP_V6';
comment on column public.ivs_master_device_basic_info.ip is 'IP地址，长度限制64字节';
comment on column public.ivs_master_device_basic_info.port is '设备连接端口';
comment on column public.ivs_master_device_basic_info.connect_code is '互联编码';
comment on column public.ivs_master_device_basic_info.unified_access_code is '设备统一编码';
comment on column public.ivs_master_device_basic_info.img_dev_parent_code is '采集设备所属采集系统编码或视图库设备所属视图库编码';
comment on column public.ivs_master_device_basic_info.img_protocol_type is '图片接入协议：0：仅支持非1400协议;1：仅支持1400协议;2：同时支持非1400协议和1400协议';
comment on column public.ivs_master_device_basic_info.img_user_name is '1400采集设备登录平台用户名';
comment on column public.ivs_master_device_basic_info.img_gw_code is '设备所属1400网关编码';
comment on column public.ivs_master_device_basic_info.collection_dev_type is '采集设备类型0-非1400设备;1-独立采集设备;2-采集系统所属采集设备;3-视图库所属采集设备';
comment on column public.ivs_master_device_basic_info.active_status is '设备活动状态';
comment on column public.ivs_master_device_basic_info.is_nat is '是否nat穿越场景';
comment on column public.ivs_master_device_basic_info.reserve is '保留字段';
alter table public.ivs_master_device_basic_info owner to postgres;
-- nvr设备信息表
create table public.ivs_nvr_device_info (
  id serial,
  protocol_type varchar(225),
  login_type integer,
  enable_schedule integer,
  status integer,
  reserve varchar(225)
);
comment on table public.ivs_nvr_device_info is 'nvr设备信息表';
comment on column public.ivs_nvr_device_info.id is '自增id';
comment on column public.ivs_nvr_device_info.protocol_type is '主设备协议类型，长度限制64字节：● TEYES：千里眼● ONVIF● HWSDK：华为SDK● HIKSDK：海康SDK';
comment on column public.ivs_nvr_device_info.login_type is '认证类型：protocolType为ONVIF时，认证类型有如下三种：– 0：None– 1：HTTP Digest– 2：WS-Username token Authentication● protocolType不是ONVIF时，取值为0：None';
comment on column public.ivs_nvr_device_info.enable_schedule is '是否启用集群调度：● 0：不启用● 1：启用，此时NVRCode不能为空。说明：该字段暂不起作用';
comment on column public.ivs_nvr_device_info.status is '设备状态：● 0：离线● 1：在线● 2：休眠';
comment on column public.ivs_nvr_device_info.reserve is '保留字段。长度限制32字节，必须保留该字段，字段内容可以置空';
alter table public.ivs_nvr_device_info owner to postgres;
-- nvr设备基本信息表
create table public.ivs_nvr_device_basic_info (
  id serial,
  ivs_nvr_device_info_id integer,
  type integer,
  serial_number varchar(225),
  code varchar(225),
  name varchar(225),
  parent_code varchar(225),
  domain_code varchar(225),
  vendor_type varchar(225),
  model varchar(225),
  ip_type integer,
  ip varchar(225),
  port integer,
  reserve varchar(225)
);
comment on table public.ivs_nvr_device_basic_info is 'nvr设备基本信息';
comment on column public.ivs_nvr_device_basic_info.id is '自增id';
comment on column public.ivs_nvr_device_basic_info.ivs_nvr_device_info_id is '关联nvr设备信息表id';
comment on column public.ivs_nvr_device_basic_info.type is '主设备类型：1：IPC、2：DVS、3：DVR、4：eNVR';
comment on column public.ivs_nvr_device_basic_info.serial_number is '序列号（SN/IMEI），长度限制64字节';
comment on column public.ivs_nvr_device_basic_info.code is '设备编码例如：32010300100201030000#6bdacabae9c546e9ab5b8688ccd85a59，长度限制64字节，详见设备编码规则';
comment on column public.ivs_nvr_device_basic_info.name is '设备名称键盘可见字符和中文，长度限制128字节';
comment on column public.ivs_nvr_device_basic_info.parent_code is '父设备编码：为空：没有父设备；非空：表示该设备被eNVR管理。例如：32010300100204010000#6bdacabae9c546e9ab5b8688ccd85a59，长度限制64字节，详见设备编码规则';
comment on column public.ivs_nvr_device_basic_info.domain_code is '域编码，例如：6bdacabae9c546e9ab5b8688ccd85a59，长度限制32字节';
comment on column public.ivs_nvr_device_basic_info.vendor_type is '设备提供商类型：（长度限制32字节）● HUAWEI● HIK● DAHUA● SUNELL● CANON● CHANGHONG● TIANDY● PANASONIC● AXIS';
comment on column public.ivs_nvr_device_basic_info.model is '设备型号由各设备厂家提供，长度限制32字节';
comment on column public.ivs_nvr_device_basic_info.ip_type is 'P地址类型：0：IP_V4,1：IP_V6';
comment on column public.ivs_nvr_device_basic_info.ip is 'IP地址，长度限制64字节';
comment on column public.ivs_nvr_device_basic_info.port is '设备连接端口';
comment on column public.ivs_nvr_device_basic_info.reserve is '保留字段';
alter table public.ivs_nvr_device_basic_info owner to postgres;
-- ivs子设备信息表
create table public.ivs_child_device_info (
  id serial,
  code varchar(225),
  name varchar(225),
  device_group_code varchar(225),
  parent_code varchar(225),
  domain_code varchar(225),
  device_model_type varchar(225),
  parent_connect_code varchar(225),
  vendor_type varchar(225),
  device_form_type integer,
  type integer,
  camera_location varchar(225),
  camera_status integer,
  status integer,
  net_type integer,
  is_support_intelligent integer,
  enable_voice integer,
  nvr_code varchar(225),
  device_create_time varchar(225),
  is_ex_domain integer,
  device_ip varchar(225),
  longitude varchar(225),
  latitude varchar(225),
  height numeric,
  is_shadow_dev integer,
  orig_dev_code varchar(225),
  orig_parent_dev_code varchar(225),
  ori_dev_domain_code varchar(225),
  have_alarm_linkage integer,
  delete_status integer,
  connect_code varchar(225),
  support_ga1400 integer,
  reserve varchar(225),
  custom_fields varchar(225),
  function_type varchar(225),
  position_type varchar(225),
  monitor_direction varchar(225),
  owner_aps_id varchar(225),
  port integer,
  place_code varchar(225),
  org_code varchar(225),
  cap_direction varchar(225),
  monitor_area_desc varchar(225)
);
comment on table public.ivs_child_device_info is '子设备信息表';
comment on column public.ivs_child_device_info.id is '自增id';
comment on column public.ivs_child_device_info.code is '设备编码,长度限制64字节';
comment on column public.ivs_child_device_info.name is '摄像机名称,键盘可见字符和中文,长度限制192字节';
comment on column public.ivs_child_device_info.device_group_code is '所属设备组编码（当请求参数groupMode=1，并且此设备响应参数isExDomain=0时，此编码为上级域挂载的设备组编码；否则此编码为设备原域的设备组编码)长度限制128字节';
comment on column public.ivs_child_device_info.parent_code is '设备编码例如：32010300100201030000#6bdacabae9c546e9ab5b8688ccd85a59，长度限制64字节，详见设备编码规则';
comment on column public.ivs_child_device_info.domain_code is '设备归属域的域编码';
comment on column public.ivs_child_device_info.device_model_type is '主设备型号';
comment on column public.ivs_child_device_info.parent_connect_code is '主设备互联编码';
comment on column public.ivs_child_device_info.vendor_type is '设备提供商类型：（长度限制32字节）● HUAWEI● HIK● DAHUA● SUNELL● CANON● CHANGHONG● TIANDY● PANASONIC● AXIS';
comment on column public.ivs_child_device_info.device_form_type is '主设备类型：● 1：IPC● 2：DVS● 3：DVR● 4：eNVR';
comment on column public.ivs_child_device_info.type is '摄像机类型：● 0：固定枪机● 1：有云台枪机● 2：球机● 3：半球-固定摄像机● 4：筒机';
comment on column public.ivs_child_device_info.camera_location is '摄像机安装位置描述键盘可见字符和中文，长度限制256字节';
comment on column public.ivs_child_device_info.camera_status is '摄像机扩展状态：● 1：正常● 2：视频丢失';
comment on column public.ivs_child_device_info.status is '设备状态：● 0：离线● 1：在线● 2：休眠';
comment on column public.ivs_child_device_info.net_type is '网络类型：● 0：有线● 1：无线';
comment on column public.ivs_child_device_info.is_support_intelligent is '是否支持智能分析：● 0：不支持● 1：支持';
comment on column public.ivs_child_device_info.enable_voice is '是否启用随路音频：● 0：停用● 1：启用';
comment on column public.ivs_child_device_info.nvr_code is '设备所属NVR编码表示该设备被该NVR管理，例如：9145a3f7c4164d3ab9e161fcb4221426，长度限制32字节';
comment on column public.ivs_child_device_info.device_create_time is '设备创建时间格式为yyyyMMddHHmmss，如20121207102035，长度限制20字节';
comment on column public.ivs_child_device_info.is_ex_domain is '是否为外域：● 0：否● 1：是';
comment on column public.ivs_child_device_info.device_ip is '前端设备IP 例如：IPV4地址 :************** IPV6地址 :fec0:90:2c02::13';
comment on column public.ivs_child_device_info.longitude is '经度，长度限制20字节';
comment on column public.ivs_child_device_info.latitude is '纬度，长度限制20字节';
comment on column public.ivs_child_device_info.height is '摄像机安装高度';
comment on column public.ivs_child_device_info.is_shadow_dev is '是否为影子摄像机● 0：否● 1：是';
comment on column public.ivs_child_device_info.orig_dev_code is '原始设备编码，长度限制64字节';
comment on column public.ivs_child_device_info.orig_parent_dev_code is '原始父设备编码，长度限制64字节';
comment on column public.ivs_child_device_info.ori_dev_domain_code is '原始域编码，长度限制32字节';
comment on column public.ivs_child_device_info.have_alarm_linkage is '是否配置告警联动计划● 0：否● 1：是';
comment on column public.ivs_child_device_info.delete_status is '删除状态● 0：待彻底删除● 1：设备正常';
comment on column public.ivs_child_device_info.connect_code is '互联编码，长度限制64字节';
comment on column public.ivs_child_device_info.support_ga1400 is '1400支持能力：● 0：不支持1400协议，即作为视频子设备● 1：仅支持1400协议，即作为视图子设备● 2：同时支持1400协议和视频能力';
comment on column public.ivs_child_device_info.reserve is '保留字段长度限制252字节，必须保留该字段，字段内容可以置空';
comment on column public.ivs_child_device_info.custom_fields is '透明字段';
comment on column public.ivs_child_device_info.function_type is '1400协议使用，功能类型，1、车辆卡口；2、人员卡口；3、微卡口；4、特征摄像机；5、普通安防；6、高空瞭望摄像机；99 其他，多选各选参数以“/”分割';
comment on column public.ivs_child_device_info.position_type is '摄像机位置类型,取值规则：1-省际检查站、2-党政机关、3-车站码头、4-中心广场、5-体育场馆、6-商业中心、7-宗教场所、8-校园周边、9-治安复杂区域、10-交通干线、11-医院周边、12-金融机构周边、13-危险物品场所周边、14-博物馆展览馆、15-重点水域、航道、96.市际检查站；97.涉外场所；98.边境沿线；99.旅游景区， 多选各参数以" /" 分隔';
comment on column public.ivs_child_device_info.monitor_direction is '1400协议使用，监视方向';
comment on column public.ivs_child_device_info.owner_aps_id is 'VCN作为采集系统时的采集系统的编码';
comment on column public.ivs_child_device_info.port is '采集设备端口号';
comment on column public.ivs_child_device_info.place_code is '安装地点行政区划。只能包含英文字母、数字、中划线和下划线。1~64个字符。';
comment on column public.ivs_child_device_info.org_code is '管辖单位代码。只能包含英文字母、数字、中划线和下划线。1~64个字符。';
comment on column public.ivs_child_device_info.cap_direction is '车辆抓拍方向 0:拍车头; 1:拍车尾';
comment on column public.ivs_child_device_info.monitor_area_desc is '1400监视区域说明。512字符';
alter table public.ivs_child_device_info owner to postgres;
DROP TABLE IF EXISTS "public"."mos_device_info";
CREATE TABLE "public"."mos_device_info" (
  "id" serial,
  "device_type" VARCHAR(255) COLLATE "pg_catalog"."default",
  "device_name" VARCHAR(255) COLLATE "pg_catalog"."default",
  "model" VARCHAR(255) COLLATE "pg_catalog"."default",
  "username" VARCHAR(255) COLLATE "pg_catalog"."default",
  "password" VARCHAR(255) COLLATE "pg_catalog"."default",
  "type" VARCHAR(255) COLLATE "pg_catalog"."default",
  "ip" VARCHAR(255) COLLATE "pg_catalog"."default",
  "port" int4,
  CONSTRAINT "mos_device_info_pkey" PRIMARY KEY ("id")
);
ALTER TABLE "public"."mos_device_info" OWNER TO "postgres";
COMMENT ON COLUMN "public"."mos_device_info"."device_type" IS '设备类型';
COMMENT ON COLUMN "public"."mos_device_info"."device_name" IS '设备名称';
COMMENT ON COLUMN "public"."mos_device_info"."model" IS '型号';
COMMENT ON COLUMN "public"."mos_device_info"."username" IS '用户名';
COMMENT ON COLUMN "public"."mos_device_info"."password" IS '密码';
COMMENT ON COLUMN "public"."mos_device_info"."type" IS '厂家';
COMMENT ON COLUMN "public"."mos_device_info"."ip" IS '地址';
COMMENT ON COLUMN "public"."mos_device_info"."port" IS '端口号';
DROP TABLE IF EXISTS "public"."camera_info";
CREATE TABLE "public"."camera_info" (
  "id" serial,
  "name" varchar(255) COLLATE "pg_catalog"."default",
  "code" varchar(64) COLLATE "pg_catalog"."default",
  "device_port" varchar(20) COLLATE "pg_catalog"."default",
  "device_ip" varchar(100) COLLATE "pg_catalog"."default",
  "play_url" varchar(255) COLLATE "pg_catalog"."default",
  "status" int4,
  "device_id" int4,
  "device_type" varchar(255) COLLATE "pg_catalog"."default"
);
COMMENT ON COLUMN "public"."camera_info"."id" IS '主键id';
COMMENT ON COLUMN "public"."camera_info"."name" IS '摄像头名称';
COMMENT ON COLUMN "public"."camera_info"."code" IS 'ivs摄像头编码';
COMMENT ON COLUMN "public"."camera_info"."device_ip" IS '摄像头ip';
COMMENT ON COLUMN "public"."camera_info"."device_port" IS '端口号';
COMMENT ON COLUMN "public"."camera_info"."play_url" IS '播放路径';
COMMENT ON COLUMN "public"."camera_info"."status" IS '设备状态：● 0：离线● 1：在线● 2：休眠';
COMMENT ON COLUMN "public"."camera_info"."device_id" IS '设备id';
COMMENT ON COLUMN "public"."camera_info"."device_type" IS '设备类型';
COMMENT ON TABLE "public"."camera_info" IS '摄像头信息表';
ALTER TABLE public.camera_info
ADD CONSTRAINT camera_info_pk PRIMARY KEY (code);
